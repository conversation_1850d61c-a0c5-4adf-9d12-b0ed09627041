#!/bin/bash

# Kafka测试脚本
# 用于启动Kafka并运行相关测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker状态..."
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker未运行，请启动Docker Desktop"
        exit 1
    fi
    log_success "Docker运行正常"
}

# 启动Kafka
start_kafka() {
    log_info "启动Kafka服务..."
    
    # 检查是否已经运行
    if docker ps | grep -q "broker"; then
        log_warning "Kafka已经在运行"
        return 0
    fi
    
    # 启动服务
    docker compose up -d broker
    
    # 等待Kafka启动
    log_info "等待Kafka启动..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker logs broker 2>&1 | grep -q "Kafka Server started"; then
            log_success "Kafka启动成功"
            return 0
        fi
        
        log_info "等待Kafka启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "Kafka启动超时"
    docker logs broker
    exit 1
}

# 停止Kafka
stop_kafka() {
    log_info "停止Kafka服务..."
    docker compose down -v
    log_success "Kafka服务已停止"
}

# 运行单元测试
run_unit_tests() {
    log_info "运行Kafka单元测试..."
    
    # 运行pkg/kafka的单元测试
    log_info "运行pkg/kafka单元测试..."
    cd pkg
    go test -v ./kafka/... -run "Test.*" -short
    cd ..
    
    log_success "单元测试完成"
}

# 运行集成测试
run_integration_tests() {
    log_info "运行Kafka集成测试..."
    
    # 确保Kafka正在运行
    start_kafka
    
    # 等待额外时间确保Kafka完全就绪
    sleep 5
    
    # 运行用户服务集成测试
    log_info "运行用户服务Kafka集成测试..."
    cd services/user-service
    go test -v ./internal/event/... -run ".*Integration.*"
    cd ../..
    
    # 运行订单服务集成测试
    log_info "运行订单服务Kafka集成测试..."
    cd services/order-service
    go test -v ./internal/event/... -run ".*Integration.*"
    cd ../..
    
    log_success "集成测试完成"
}

# 运行所有测试
run_all_tests() {
    log_info "运行所有Kafka测试..."
    
    run_unit_tests
    run_integration_tests
    
    log_success "所有测试完成"
}

# 清理测试环境
cleanup() {
    log_info "清理测试环境..."
    
    # 停止所有容器
    docker compose down -v
    
    # 清理测试主题（如果需要）
    log_info "清理完成"
}

# 显示帮助信息
show_help() {
    echo "Kafka测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start-kafka     启动Kafka服务"
    echo "  stop-kafka      停止Kafka服务"
    echo "  unit-tests      运行单元测试"
    echo "  integration     运行集成测试"
    echo "  all-tests       运行所有测试"
    echo "  cleanup         清理测试环境"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start-kafka"
    echo "  $0 all-tests"
    echo "  $0 cleanup"
}

# 主函数
main() {
    case "${1:-all-tests}" in
        "start-kafka")
            check_docker
            start_kafka
            ;;
        "stop-kafka")
            check_docker
            stop_kafka
            ;;
        "unit-tests")
            run_unit_tests
            ;;
        "integration")
            check_docker
            run_integration_tests
            ;;
        "all-tests")
            check_docker
            run_all_tests
            ;;
        "cleanup")
            check_docker
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 捕获退出信号，确保清理
trap cleanup EXIT

# 运行主函数
main "$@"
