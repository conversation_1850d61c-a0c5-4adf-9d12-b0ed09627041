package kafka

import (
	"fmt"
	"time"
)

// EventMessage Kafka事件消息结构
type EventMessage struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
	Version   string                 `json:"version"`
}

// NewEventMessage 创建新的事件消息
func NewEventMessage(eventType, source string, data map[string]interface{}) *EventMessage {
	return &EventMessage{
		ID:        fmt.Sprintf("event-%d", time.Now().UnixNano()),
		Type:      eventType,
		Data:      data,
		Timestamp: time.Now(),
		Source:    source,
		Version:   "1.0",
	}
}
