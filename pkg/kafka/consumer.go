package kafka

import (
	"context"
	"log"

	"github.com/segmentio/kafka-go"
)

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	Brokers []string
	Topic   string
	GroupID string
}

// Consumer Kafka消费者
type Consumer struct {
	reader *kafka.Reader
}

// NewConsumer 创建新的Kafka消费者
func NewConsumer(config ConsumerConfig) *Consumer {
	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:  config.Brokers,
		Topic:    config.Topic,
		GroupID:  config.GroupID,
		MinBytes: 10e3, // 10KB
		MaxBytes: 10e6, // 10MB
	})

	return &Consumer{
		reader: reader,
	}
}

// ReadMessage 读取消息
func (c *Consumer) ReadMessage(ctx context.Context) (kafka.Message, error) {
	message, err := c.reader.ReadMessage(ctx)
	if err != nil {
		log.Printf("Error reading message: %v", err)
		return kafka.Message{}, err
	}

	log.Printf("Message received from topic %s: key=%s", message.Topic, string(message.Key))
	return message, nil
}

// Close 关闭消费者
func (c *Consumer) Close() error {
	if c.reader != nil {
		return c.reader.Close()
	}
	return nil
}
