package kafka

import (
	"context"
	"log"

	"github.com/segmentio/kafka-go"
)

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	Brokers     []string
	Topic       string
	GroupID     string
	StartOffset int64 // 可选：指定起始偏移量
}

// Consumer Kafka消费者
type Consumer struct {
	reader *kafka.Reader
}

// NewConsumer 创建新的Kafka消费者
func NewConsumer(config ConsumerConfig) *Consumer {
	startOffset := config.StartOffset
	if startOffset == 0 {
		startOffset = kafka.FirstOffset // 默认从头开始读取
	}

	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:     config.Brokers,
		Topic:       config.Topic,
		GroupID:     config.GroupID,
		MinBytes:    1,           // 1字节 - 立即读取
		MaxBytes:    10e6,        // 10MB
		StartOffset: startOffset, // 可配置的起始偏移量
	})

	return &Consumer{
		reader: reader,
	}
}

// ReadMessage 读取消息
func (c *Consumer) ReadMessage(ctx context.Context) (kafka.Message, error) {
	message, err := c.reader.ReadMessage(ctx)
	if err != nil {
		log.Printf("Error reading message: %v", err)
		return kafka.Message{}, err
	}

	log.Printf("Message received from topic %s: key=%s", message.Topic, string(message.Key))
	return message, nil
}

// Close 关闭消费者
func (c *Consumer) Close() error {
	if c.reader != nil {
		return c.reader.Close()
	}
	return nil
}
