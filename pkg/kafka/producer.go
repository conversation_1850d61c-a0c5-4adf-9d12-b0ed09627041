package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/segmentio/kafka-go"
)

// Producer Ka<PERSON><PERSON>生产者配置
type Producer struct {
	writer *kafka.Writer
	topic  string
}

// ProducerConfig Kafka生产者配置
type ProducerConfig struct {
	Brokers []string
	Topic   string
}

// NewProducer 创建Kafka生产者
func NewProducer(config ProducerConfig) *Producer {
	writer := &kafka.Writer{
		Addr:         kafka.TCP(config.Brokers...),
		Topic:        config.Topic,
		Balancer:     &kafka.LeastBytes{},
		RequiredAcks: kafka.RequireOne,
		Async:        false, // 同步发送确保可靠性
		Compression:  kafka.Snappy,
		BatchTimeout: 10 * time.Millisecond,
		BatchSize:    100,
	}

	return &Producer{
		writer: writer,
		topic:  config.Topic,
	}
}

// PublishEvent 发布事件到Kafka
func (p *Producer) PublishEvent(ctx context.Context, key string, event interface{}) error {
	eventData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	message := kafka.Message{
		Key:   []byte(key),
		Value: eventData,
		Time:  time.Now(),
	}

	err = p.writer.WriteMessages(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to write message to kafka: %w", err)
	}

	log.Printf("Event published to Kafka topic %s: key=%s", p.topic, key)
	return nil
}

// Close 关闭Kafka生产者
func (p *Producer) Close() error {
	return p.writer.Close()
}

// Consumer Kafka消费者
type Consumer struct {
	reader *kafka.Reader
}

// ConsumerConfig Kafka消费者配置
type ConsumerConfig struct {
	Brokers  []string
	Topic    string
	GroupID  string
	MinBytes int
	MaxBytes int
}

// NewConsumer 创建Kafka消费者
func NewConsumer(config ConsumerConfig) *Consumer {
	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:  config.Brokers,
		Topic:    config.Topic,
		GroupID:  config.GroupID,
		MinBytes: config.MinBytes,
		MaxBytes: config.MaxBytes,
	})

	return &Consumer{
		reader: reader,
	}
}

// ReadMessage 读取消息
func (c *Consumer) ReadMessage(ctx context.Context) (kafka.Message, error) {
	return c.reader.ReadMessage(ctx)
}

// Close 关闭Kafka消费者
func (c *Consumer) Close() error {
	return c.reader.Close()
}

// EventMessage Kafka事件消息结构
type EventMessage struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
	Version   string                 `json:"version"`
}

// NewEventMessage 创建新的事件消息
func NewEventMessage(eventType, source string, data map[string]interface{}) *EventMessage {
	return &EventMessage{
		ID:        fmt.Sprintf("event-%d", time.Now().UnixNano()),
		Type:      eventType,
		Data:      data,
		Timestamp: time.Now(),
		Source:    source,
		Version:   "1.0",
	}
}
