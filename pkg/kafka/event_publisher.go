package kafka

import (
	"context"
	"fmt"
	"time"
)

// EventProducer 事件生产者接口
type EventProducer interface {
	PublishEvent(ctx context.Context, key string, event interface{}) error
	Close() error
}

// KafkaEventPublisher Kafka事件发布器
type KafkaEventPublisher struct {
	producer EventProducer
	source   string
}

// NewKafkaEventPublisher 创建Kafka事件发布器
func NewKafkaEventPublisher(brokers []string, topic, source string) (*KafkaEventPublisher, error) {
	config := ProducerConfig{
		Brokers: brokers,
		Topic:   topic,
	}

	producer := NewProducer(config)

	return &KafkaEventPublisher{
		producer: producer,
		source:   source,
	}, nil
}

// PublishUserEvent 发布用户相关事件
func (k *KafkaEventPublisher) PublishUserEvent(eventType string, data map[string]interface{}) error {
	event := NewEventMessage(eventType, k.source, data)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用用户ID作为分区键，确保同一用户的事件有序
	key := fmt.Sprintf("user-%v", data["user_id"])

	return k.producer.PublishEvent(ctx, key, event)
}

// PublishOrderEvent 发布订单相关事件
func (k *KafkaEventPublisher) PublishOrderEvent(eventType string, data map[string]interface{}) error {
	event := NewEventMessage(eventType, k.source, data)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用订单ID作为分区键，确保同一订单的事件有序
	key := fmt.Sprintf("order-%v", data["order_id"])

	return k.producer.PublishEvent(ctx, key, event)
}

// PublishInventoryEvent 发布库存相关事件
func (k *KafkaEventPublisher) PublishInventoryEvent(eventType string, data map[string]interface{}) error {
	event := NewEventMessage(eventType, k.source, data)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用产品ID作为分区键，确保同一产品的事件有序
	key := fmt.Sprintf("product-%v", data["product_id"])

	return k.producer.PublishEvent(ctx, key, event)
}

// PublishPaymentEvent 发布支付相关事件
func (k *KafkaEventPublisher) PublishPaymentEvent(eventType string, data map[string]interface{}) error {
	event := NewEventMessage(eventType, k.source, data)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用支付ID作为分区键，确保同一支付的事件有序
	key := fmt.Sprintf("payment-%v", data["payment_id"])

	return k.producer.PublishEvent(ctx, key, event)
}

// Close 关闭事件发布器
func (k *KafkaEventPublisher) Close() error {
	return k.producer.Close()
}
