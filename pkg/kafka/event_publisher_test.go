package kafka

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockProducer 模拟Producer用于单元测试
type MockProducer struct {
	messages []MockMessage
}

type MockMessage struct {
	Key   string
	Value []byte
}

func (m *MockProducer) PublishEvent(ctx context.Context, key string, event interface{}) error {
	eventData, err := json.Marshal(event)
	if err != nil {
		return err
	}

	m.messages = append(m.messages, MockMessage{
		Key:   key,
		Value: eventData,
	})
	return nil
}

func (m *MockProducer) Close() error {
	return nil
}

// TestKafkaEventPublisherCreation 测试KafkaEventPublisher创建
func TestKafkaEventPublisherCreation(t *testing.T) {
	brokers := []string{"localhost:9092"}
	topic := "test-topic"
	source := "test-service"

	publisher, err := NewKafkaEventPublisher(brokers, topic, source)
	assert.NoError(t, err)
	assert.NotNil(t, publisher)
	assert.NotNil(t, publisher.producer)
	assert.Equal(t, source, publisher.source)
}

// TestPublishUserEvent 测试发布用户事件
func TestPublishUserEvent(t *testing.T) {
	mockProducer := &MockProducer{}
	publisher := &KafkaEventPublisher{
		producer: mockProducer,
		source:   "user-service",
	}

	eventType := "user.registered"
	data := map[string]interface{}{
		"user_id":  "user-123",
		"username": "testuser",
		"email":    "<EMAIL>",
	}

	err := publisher.PublishUserEvent(eventType, data)
	assert.NoError(t, err)

	// 验证消息被发送
	assert.Len(t, mockProducer.messages, 1)
	message := mockProducer.messages[0]

	// 验证分区键
	assert.Equal(t, "user-user-123", message.Key)

	// 验证消息内容
	var event EventMessage
	err = json.Unmarshal(message.Value, &event)
	require.NoError(t, err)

	assert.NotEmpty(t, event.ID)
	assert.Equal(t, eventType, event.Type)
	assert.Equal(t, data, event.Data)
	assert.Equal(t, "user-service", event.Source)
	assert.Equal(t, "1.0", event.Version)
	assert.WithinDuration(t, time.Now(), event.Timestamp, 5*time.Second)
}

// TestPublishOrderEvent 测试发布订单事件
func TestPublishOrderEvent(t *testing.T) {
	mockProducer := &MockProducer{}
	publisher := &KafkaEventPublisher{
		producer: mockProducer,
		source:   "order-service",
	}

	eventType := "order.created"
	data := map[string]interface{}{
		"order_id":     "order-456",
		"user_id":      "user-123",
		"total_amount": 99.99,
		"items": []interface{}{
			map[string]interface{}{
				"product_id": "prod-001",
				"quantity":   float64(2), // JSON会将数字转换为float64
				"price":      49.99,
			},
		},
	}

	err := publisher.PublishOrderEvent(eventType, data)
	assert.NoError(t, err)

	// 验证消息被发送
	assert.Len(t, mockProducer.messages, 1)
	message := mockProducer.messages[0]

	// 验证分区键
	assert.Equal(t, "order-order-456", message.Key)

	// 验证消息内容
	var event EventMessage
	err = json.Unmarshal(message.Value, &event)
	require.NoError(t, err)

	assert.NotEmpty(t, event.ID)
	assert.Equal(t, eventType, event.Type)
	assert.Equal(t, data, event.Data)
	assert.Equal(t, "order-service", event.Source)
	assert.Equal(t, "1.0", event.Version)
}

// TestPublishInventoryEvent 测试发布库存事件
func TestPublishInventoryEvent(t *testing.T) {
	mockProducer := &MockProducer{}
	publisher := &KafkaEventPublisher{
		producer: mockProducer,
		source:   "inventory-service",
	}

	eventType := "inventory.updated"
	data := map[string]interface{}{
		"product_id": "prod-789",
		"quantity":   float64(50), // JSON会将数字转换为float64
		"location":   "warehouse-A",
	}

	err := publisher.PublishInventoryEvent(eventType, data)
	assert.NoError(t, err)

	// 验证消息被发送
	assert.Len(t, mockProducer.messages, 1)
	message := mockProducer.messages[0]

	// 验证分区键
	assert.Equal(t, "product-prod-789", message.Key)

	// 验证消息内容
	var event EventMessage
	err = json.Unmarshal(message.Value, &event)
	require.NoError(t, err)

	assert.Equal(t, eventType, event.Type)
	assert.Equal(t, data, event.Data)
	assert.Equal(t, "inventory-service", event.Source)
}

// TestPublishPaymentEvent 测试发布支付事件
func TestPublishPaymentEvent(t *testing.T) {
	mockProducer := &MockProducer{}
	publisher := &KafkaEventPublisher{
		producer: mockProducer,
		source:   "payment-service",
	}

	eventType := "payment.completed"
	data := map[string]interface{}{
		"payment_id": "pay-999",
		"order_id":   "order-456",
		"amount":     99.99,
		"method":     "credit_card",
		"status":     "completed",
	}

	err := publisher.PublishPaymentEvent(eventType, data)
	assert.NoError(t, err)

	// 验证消息被发送
	assert.Len(t, mockProducer.messages, 1)
	message := mockProducer.messages[0]

	// 验证分区键
	assert.Equal(t, "payment-pay-999", message.Key)

	// 验证消息内容
	var event EventMessage
	err = json.Unmarshal(message.Value, &event)
	require.NoError(t, err)

	assert.Equal(t, eventType, event.Type)
	assert.Equal(t, data, event.Data)
	assert.Equal(t, "payment-service", event.Source)
}

// TestEventStructure 测试事件结构
func TestEventStructure(t *testing.T) {
	mockProducer := &MockProducer{}
	publisher := &KafkaEventPublisher{
		producer: mockProducer,
		source:   "test-service",
	}

	eventType := "test.event"
	data := map[string]interface{}{
		"test_id": "test-123",
		"message": "test message",
	}

	err := publisher.PublishUserEvent(eventType, data)
	require.NoError(t, err)

	// 验证事件结构
	var event EventMessage
	err = json.Unmarshal(mockProducer.messages[0].Value, &event)
	require.NoError(t, err)

	// 验证所有必需字段
	assert.NotEmpty(t, event.ID, "Event ID should not be empty")
	assert.Equal(t, eventType, event.Type, "Event type should match")
	assert.Equal(t, data, event.Data, "Event data should match")
	assert.Equal(t, "test-service", event.Source, "Event source should match")
	assert.Equal(t, "1.0", event.Version, "Event version should be 1.0")
	assert.False(t, event.Timestamp.IsZero(), "Event timestamp should not be zero")

	// 验证时间戳在合理范围内
	now := time.Now()
	assert.True(t, event.Timestamp.Before(now.Add(time.Second)), "Timestamp should be recent")
	assert.True(t, event.Timestamp.After(now.Add(-time.Minute)), "Timestamp should not be too old")
}

// TestMultipleEvents 测试发布多个事件
func TestMultipleEvents(t *testing.T) {
	mockProducer := &MockProducer{}
	publisher := &KafkaEventPublisher{
		producer: mockProducer,
		source:   "multi-test-service",
	}

	// 发布多种类型的事件
	events := []struct {
		publishFunc func(string, map[string]interface{}) error
		eventType   string
		data        map[string]interface{}
		expectedKey string
	}{
		{
			publishFunc: publisher.PublishUserEvent,
			eventType:   "user.created",
			data:        map[string]interface{}{"user_id": "user-001"},
			expectedKey: "user-user-001",
		},
		{
			publishFunc: publisher.PublishOrderEvent,
			eventType:   "order.created",
			data:        map[string]interface{}{"order_id": "order-001"},
			expectedKey: "order-order-001",
		},
		{
			publishFunc: publisher.PublishInventoryEvent,
			eventType:   "inventory.updated",
			data:        map[string]interface{}{"product_id": "prod-001"},
			expectedKey: "product-prod-001",
		},
		{
			publishFunc: publisher.PublishPaymentEvent,
			eventType:   "payment.processed",
			data:        map[string]interface{}{"payment_id": "pay-001"},
			expectedKey: "payment-pay-001",
		},
	}

	for i, event := range events {
		err := event.publishFunc(event.eventType, event.data)
		require.NoError(t, err, "Failed to publish event %d", i)
	}

	// 验证所有事件都被发送
	assert.Len(t, mockProducer.messages, len(events))

	// 验证每个事件的分区键
	for i, event := range events {
		assert.Equal(t, event.expectedKey, mockProducer.messages[i].Key, "Partition key mismatch for event %d", i)
	}
}
