package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	testBroker = "localhost:9092"
	testTopic  = "test-topic"
)

// TestMessage 测试消息结构
type TestMessage struct {
	ID        string    `json:"id"`
	Type      string    `json:"type"`
	Data      string    `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// setupTestTopic 创建测试主题
func setupTestTopic(t *testing.T) {
	conn, err := kafka.Dial("tcp", testBroker)
	require.NoError(t, err)
	defer conn.Close()

	controller, err := conn.Controller()
	require.NoError(t, err)

	controllerConn, err := kafka.Dial("tcp", fmt.Sprintf("%s:%d", controller.Host, controller.Port))
	require.NoError(t, err)
	defer controllerConn.Close()

	topicConfigs := []kafka.TopicConfig{
		{
			Topic:             testTopic,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
	}

	err = controllerConn.CreateTopics(topicConfigs...)
	if err != nil {
		// 如果主题已存在，忽略错误
		t.Logf("Topic creation warning: %v", err)
	}
}

// cleanupTestTopic 清理测试主题
func cleanupTestTopic(t *testing.T) {
	conn, err := kafka.Dial("tcp", testBroker)
	if err != nil {
		t.Logf("Failed to connect for cleanup: %v", err)
		return
	}
	defer conn.Close()

	controller, err := conn.Controller()
	if err != nil {
		t.Logf("Failed to get controller for cleanup: %v", err)
		return
	}

	controllerConn, err := kafka.Dial("tcp", fmt.Sprintf("%s:%d", controller.Host, controller.Port))
	if err != nil {
		t.Logf("Failed to connect to controller for cleanup: %v", err)
		return
	}
	defer controllerConn.Close()

	err = controllerConn.DeleteTopics(testTopic)
	if err != nil {
		t.Logf("Failed to delete topic: %v", err)
	}
}

// TestProducerCreation 测试Producer创建
func TestProducerCreation(t *testing.T) {
	config := ProducerConfig{
		Brokers: []string{testBroker},
		Topic:   testTopic,
	}
	producer := NewProducer(config)
	assert.NotNil(t, producer)
	assert.Equal(t, testTopic, producer.topic)
}

// TestProducerSendMessage 测试发送消息
func TestProducerSendMessage(t *testing.T) {
	setupTestTopic(t)
	defer cleanupTestTopic(t)

	config := ProducerConfig{
		Brokers: []string{testBroker},
		Topic:   testTopic,
	}
	producer := NewProducer(config)

	testMsg := TestMessage{
		ID:        "test-001",
		Type:      "test-event",
		Data:      "test data",
		Timestamp: time.Now(),
	}

	ctx := context.Background()
	err := producer.PublishEvent(ctx, "test-key", testMsg)
	assert.NoError(t, err)
}

// TestProducerSendMultipleMessages 测试发送多条消息
func TestProducerSendMultipleMessages(t *testing.T) {
	setupTestTopic(t)
	defer cleanupTestTopic(t)

	config := ProducerConfig{
		Brokers: []string{testBroker},
		Topic:   testTopic,
	}
	producer := NewProducer(config)

	messageCount := 10
	ctx := context.Background()
	for i := 0; i < messageCount; i++ {
		testMsg := TestMessage{
			ID:        fmt.Sprintf("test-%03d", i),
			Type:      "test-event",
			Data:      fmt.Sprintf("test data %d", i),
			Timestamp: time.Now(),
		}

		err := producer.PublishEvent(ctx, fmt.Sprintf("key-%d", i), testMsg)
		assert.NoError(t, err)
	}
}

// TestConsumerCreation 测试Consumer创建
func TestConsumerCreation(t *testing.T) {
	config := ConsumerConfig{
		Brokers: []string{testBroker},
		Topic:   testTopic,
		GroupID: "test-group",
	}
	consumer := NewConsumer(config)
	assert.NotNil(t, consumer)
}

// TestProducerConsumerIntegration 测试Producer和Consumer集成
func TestProducerConsumerIntegration(t *testing.T) {
	setupTestTopic(t)
	defer cleanupTestTopic(t)

	// 创建Producer
	producerConfig := ProducerConfig{
		Brokers: []string{testBroker},
		Topic:   testTopic,
	}
	producer := NewProducer(producerConfig)

	// 创建Consumer
	consumerConfig := ConsumerConfig{
		Brokers: []string{testBroker},
		Topic:   testTopic,
		GroupID: "integration-test-group",
	}
	consumer := NewConsumer(consumerConfig)

	// 发送测试消息
	testMsg := TestMessage{
		ID:        "integration-test-001",
		Type:      "integration-event",
		Data:      "integration test data",
		Timestamp: time.Now(),
	}

	ctx := context.Background()
	err := producer.PublishEvent(ctx, "integration-key", testMsg)
	require.NoError(t, err)

	// 等待消息传播
	time.Sleep(2 * time.Second)

	// 消费消息
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	message, err := consumer.ReadMessage(ctx)
	require.NoError(t, err)
	assert.Equal(t, "integration-key", string(message.Key))

	var receivedMsg TestMessage
	err = json.Unmarshal(message.Value, &receivedMsg)
	require.NoError(t, err)
	assert.Equal(t, testMsg.ID, receivedMsg.ID)
	assert.Equal(t, testMsg.Type, receivedMsg.Type)
	assert.Equal(t, testMsg.Data, receivedMsg.Data)
}
