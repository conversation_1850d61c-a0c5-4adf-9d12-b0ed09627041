// user.proto
syntax = "proto3";

package user;

option go_package = "./user";

message RegisterRequest {
  string username = 1;
  string email = 2;
  string password = 3;
}

message GetUserResponse {
  string user_id = 1;
  string username = 2;
  string email = 3;
}

service UserService {
  rpc Register (RegisterRequest) returns (GetUserResponse);
  rpc GetUser (GetUserResponse) returns (GetUserResponse);
}