package config

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Brokers []string `mapstructure:"brokers" yaml:"brokers"`
	Topic   string   `mapstructure:"topic" yaml:"topic"`
	Enabled bool     `mapstructure:"enabled" yaml:"enabled"`
}

// GetKafkaBrokers 获取Kafka代理地址
func (c *KafkaConfig) GetKafkaBrokers() []string {
	if c.Brokers == nil || len(c.Brokers) == 0 {
		return []string{"localhost:9092"} // 默认值
	}
	return c.Brokers
}

// GetKafkaTopic 获取Kafka主题
func (c *KafkaConfig) GetKafkaTopic() string {
	if c.Topic == "" {
		return "pay-mall-events" // 默认主题
	}
	return c.Topic
}

// IsKafkaEnabled 检查Kafka是否启用
func (c *KafkaConfig) IsKafkaEnabled() bool {
	return c.Enabled
}
