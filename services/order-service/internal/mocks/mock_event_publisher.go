// Code generated by MockGen. DO NOT EDIT.
// Source: internal/event/event.go
//
// Generated by this command:
//
//	mockgen -source=internal/event/event.go -destination=internal/mocks/mock_event_publisher.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	event "pay-mall/services/order-service/internal/event"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockEventPublisher is a mock of EventPublisher interface.
type MockEventPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockEventPublisherMockRecorder
	isgomock struct{}
}

// MockEventPublisherMockRecorder is the mock recorder for MockEventPublisher.
type MockEventPublisherMockRecorder struct {
	mock *MockEventPublisher
}

// NewMockEventPublisher creates a new mock instance.
func NewMockEventPublisher(ctrl *gomock.Controller) *MockEventPublisher {
	mock := &MockEventPublisher{ctrl: ctrl}
	mock.recorder = &MockEventPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventPublisher) EXPECT() *MockEventPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockEventPublisher) Publish(arg0 event.Event) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockEventPublisherMockRecorder) Publish(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockEventPublisher)(nil).Publish), arg0)
}

// MockEvent is a mock of Event interface.
type MockEvent struct {
	ctrl     *gomock.Controller
	recorder *MockEventMockRecorder
	isgomock struct{}
}

// MockEventMockRecorder is the mock recorder for MockEvent.
type MockEventMockRecorder struct {
	mock *MockEvent
}

// NewMockEvent creates a new mock instance.
func NewMockEvent(ctrl *gomock.Controller) *MockEvent {
	mock := &MockEvent{ctrl: ctrl}
	mock.recorder = &MockEventMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEvent) EXPECT() *MockEventMockRecorder {
	return m.recorder
}

// GetEventData mocks base method.
func (m *MockEvent) GetEventData() any {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEventData")
	ret0, _ := ret[0].(any)
	return ret0
}

// GetEventData indicates an expected call of GetEventData.
func (mr *MockEventMockRecorder) GetEventData() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEventData", reflect.TypeOf((*MockEvent)(nil).GetEventData))
}

// GetEventType mocks base method.
func (m *MockEvent) GetEventType() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEventType")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetEventType indicates an expected call of GetEventType.
func (mr *MockEventMockRecorder) GetEventType() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEventType", reflect.TypeOf((*MockEvent)(nil).GetEventType))
}

// GetTimestamp mocks base method.
func (m *MockEvent) GetTimestamp() time.Time {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimestamp")
	ret0, _ := ret[0].(time.Time)
	return ret0
}

// GetTimestamp indicates an expected call of GetTimestamp.
func (mr *MockEventMockRecorder) GetTimestamp() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimestamp", reflect.TypeOf((*MockEvent)(nil).GetTimestamp))
}
