// Code generated by MockGen. DO NOT EDIT.
// Source: internal/repository/order_repository.go
//
// Generated by this command:
//
//	mockgen -source=internal/repository/order_repository.go -destination=internal/mocks/mock_order_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	model "pay-mall/services/order-service/internal/model"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockOrderRepository is a mock of OrderRepository interface.
type MockOrderRepository struct {
	ctrl     *gomock.Controller
	recorder *MockOrderRepositoryMockRecorder
	isgomock struct{}
}

// MockOrderRepositoryMockRecorder is the mock recorder for MockOrderRepository.
type MockOrderRepositoryMockRecorder struct {
	mock *MockOrderRepository
}

// NewMockOrderRepository creates a new mock instance.
func NewMockOrderRepository(ctrl *gomock.Controller) *MockOrderRepository {
	mock := &MockOrderRepository{ctrl: ctrl}
	mock.recorder = &MockOrderRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderRepository) EXPECT() *MockOrderRepositoryMockRecorder {
	return m.recorder
}

// CountOrders mocks base method.
func (m *MockOrderRepository) CountOrders() (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountOrders")
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountOrders indicates an expected call of CountOrders.
func (mr *MockOrderRepositoryMockRecorder) CountOrders() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountOrders", reflect.TypeOf((*MockOrderRepository)(nil).CountOrders))
}

// CountOrdersByUserID mocks base method.
func (m *MockOrderRepository) CountOrdersByUserID(userID string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountOrdersByUserID", userID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountOrdersByUserID indicates an expected call of CountOrdersByUserID.
func (mr *MockOrderRepositoryMockRecorder) CountOrdersByUserID(userID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountOrdersByUserID", reflect.TypeOf((*MockOrderRepository)(nil).CountOrdersByUserID), userID)
}

// Create mocks base method.
func (m *MockOrderRepository) Create(order *model.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", order)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockOrderRepositoryMockRecorder) Create(order any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockOrderRepository)(nil).Create), order)
}

// CreateOrderItem mocks base method.
func (m *MockOrderRepository) CreateOrderItem(item *model.OrderItem) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrderItem", item)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrderItem indicates an expected call of CreateOrderItem.
func (mr *MockOrderRepositoryMockRecorder) CreateOrderItem(item any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrderItem", reflect.TypeOf((*MockOrderRepository)(nil).CreateOrderItem), item)
}

// Delete mocks base method.
func (m *MockOrderRepository) Delete(id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockOrderRepositoryMockRecorder) Delete(id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockOrderRepository)(nil).Delete), id)
}

// DeleteItems mocks base method.
func (m *MockOrderRepository) DeleteItems(orderID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteItems", orderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteItems indicates an expected call of DeleteItems.
func (mr *MockOrderRepositoryMockRecorder) DeleteItems(orderID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteItems", reflect.TypeOf((*MockOrderRepository)(nil).DeleteItems), orderID)
}

// FindByID mocks base method.
func (m *MockOrderRepository) FindByID(id string) (*model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", id)
	ret0, _ := ret[0].(*model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockOrderRepositoryMockRecorder) FindByID(id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockOrderRepository)(nil).FindByID), id)
}

// FindByUserID mocks base method.
func (m *MockOrderRepository) FindByUserID(userID string, limit, offset int32) ([]*model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByUserID", userID, limit, offset)
	ret0, _ := ret[0].([]*model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByUserID indicates an expected call of FindByUserID.
func (mr *MockOrderRepositoryMockRecorder) FindByUserID(userID, limit, offset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByUserID", reflect.TypeOf((*MockOrderRepository)(nil).FindByUserID), userID, limit, offset)
}

// FindItemsByOrderID mocks base method.
func (m *MockOrderRepository) FindItemsByOrderID(orderID string) ([]*model.OrderItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindItemsByOrderID", orderID)
	ret0, _ := ret[0].([]*model.OrderItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindItemsByOrderID indicates an expected call of FindItemsByOrderID.
func (mr *MockOrderRepositoryMockRecorder) FindItemsByOrderID(orderID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindItemsByOrderID", reflect.TypeOf((*MockOrderRepository)(nil).FindItemsByOrderID), orderID)
}

// ListOrders mocks base method.
func (m *MockOrderRepository) ListOrders(limit, offset int32) ([]*model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOrders", limit, offset)
	ret0, _ := ret[0].([]*model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOrders indicates an expected call of ListOrders.
func (mr *MockOrderRepositoryMockRecorder) ListOrders(limit, offset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOrders", reflect.TypeOf((*MockOrderRepository)(nil).ListOrders), limit, offset)
}

// UpdateStatus mocks base method.
func (m *MockOrderRepository) UpdateStatus(id, status string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateStatus", id, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatus indicates an expected call of UpdateStatus.
func (mr *MockOrderRepositoryMockRecorder) UpdateStatus(id, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatus", reflect.TypeOf((*MockOrderRepository)(nil).UpdateStatus), id, status)
}
