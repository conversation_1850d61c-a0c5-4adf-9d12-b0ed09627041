package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"pay-mall/services/order-service/internal/model"
	"pay-mall/services/order-service/internal/query"
)

// OrderRepository 订单数据访问接口
type OrderRepository interface {
	Create(order *model.Order) error
	CreateOrderItem(item *model.OrderItem) error
	FindByID(id string) (*model.Order, error)
	FindByUserID(userID string, limit, offset int32) ([]*model.Order, error)
	FindItemsByOrderID(orderID string) ([]*model.OrderItem, error)
	UpdateStatus(id, status string) error
	Delete(id string) error
	DeleteItems(orderID string) error
	ListOrders(limit, offset int32) ([]*model.Order, error)
	CountOrders() (int64, error)
	CountOrdersByUserID(userID string) (int64, error)
}

// orderRepository 订单仓库实现
type orderRepository struct {
	q *query.Queries
}

// NewOrderRepository 创建订单仓库实例
func NewOrderRepository(db *sql.DB) OrderRepository {
	return &orderRepository{q: query.New(db)}
}

// Create 创建订单
func (r *orderRepository) Create(order *model.Order) error {
	ctx := context.Background()
	return r.q.CreateOrder(ctx, query.CreateOrderParams{
		ID:          order.ID,
		UserID:      order.UserID,
		TotalAmount: fmt.Sprintf("%.2f", order.TotalAmount),
		Status:      order.Status,
		CreatedAt:   order.CreatedAt,
		UpdatedAt:   order.UpdatedAt,
	})
}

// CreateOrderItem 创建订单项
func (r *orderRepository) CreateOrderItem(item *model.OrderItem) error {
	ctx := context.Background()
	return r.q.CreateOrderItem(ctx, query.CreateOrderItemParams{
		ID:        item.ID,
		OrderID:   item.OrderID,
		ProductID: item.ProductID,
		Quantity:  int32(item.Quantity),
		Price:     fmt.Sprintf("%.2f", item.Price),
	})
}

// FindByID 根据ID查找订单
func (r *orderRepository) FindByID(id string) (*model.Order, error) {
	ctx := context.Background()
	o, err := r.q.GetOrderByID(ctx, id)
	if err != nil {
		return nil, err
	}

	totalAmount, err := strconv.ParseFloat(o.TotalAmount, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse total amount: %w", err)
	}

	return &model.Order{
		ID:          o.ID,
		UserID:      o.UserID,
		TotalAmount: totalAmount,
		Status:      o.Status,
		CreatedAt:   o.CreatedAt,
		UpdatedAt:   o.UpdatedAt,
	}, nil
}

// FindByUserID 根据用户ID查找订单
func (r *orderRepository) FindByUserID(userID string, limit, offset int32) ([]*model.Order, error) {
	ctx := context.Background()
	orders, err := r.q.GetOrdersByUserID(ctx, query.GetOrdersByUserIDParams{
		UserID: userID,
		Limit:  limit,
		Offset: offset,
	})
	if err != nil {
		return nil, err
	}

	var result []*model.Order
	for _, o := range orders {
		totalAmount, err := strconv.ParseFloat(o.TotalAmount, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse total amount: %w", err)
		}

		result = append(result, &model.Order{
			ID:          o.ID,
			UserID:      o.UserID,
			TotalAmount: totalAmount,
			Status:      o.Status,
			CreatedAt:   o.CreatedAt,
			UpdatedAt:   o.UpdatedAt,
		})
	}
	return result, nil
}

// FindItemsByOrderID 根据订单ID查找订单项
func (r *orderRepository) FindItemsByOrderID(orderID string) ([]*model.OrderItem, error) {
	ctx := context.Background()
	items, err := r.q.GetOrderItemsByOrderID(ctx, orderID)
	if err != nil {
		return nil, err
	}

	var result []*model.OrderItem
	for _, item := range items {
		price, err := strconv.ParseFloat(item.Price, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse price: %w", err)
		}

		result = append(result, &model.OrderItem{
			ID:        item.ID,
			OrderID:   item.OrderID,
			ProductID: item.ProductID,
			Quantity:  int(item.Quantity),
			Price:     price,
		})
	}
	return result, nil
}

// UpdateStatus 更新订单状态
func (r *orderRepository) UpdateStatus(id, status string) error {
	ctx := context.Background()
	return r.q.UpdateOrderStatus(ctx, query.UpdateOrderStatusParams{
		Status:    status,
		UpdatedAt: sql.NullTime{Time: time.Now(), Valid: true},
		ID:        id,
	})
}

// Delete 删除订单
func (r *orderRepository) Delete(id string) error {
	ctx := context.Background()
	return r.q.DeleteOrder(ctx, id)
}

// DeleteItems 删除订单项
func (r *orderRepository) DeleteItems(orderID string) error {
	ctx := context.Background()
	return r.q.DeleteOrderItems(ctx, orderID)
}

// ListOrders 分页查询订单列表
func (r *orderRepository) ListOrders(limit, offset int32) ([]*model.Order, error) {
	ctx := context.Background()
	orders, err := r.q.ListOrders(ctx, query.ListOrdersParams{
		Limit:  limit,
		Offset: offset,
	})
	if err != nil {
		return nil, err
	}

	var result []*model.Order
	for _, o := range orders {
		totalAmount, err := strconv.ParseFloat(o.TotalAmount, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse total amount: %w", err)
		}

		result = append(result, &model.Order{
			ID:          o.ID,
			UserID:      o.UserID,
			TotalAmount: totalAmount,
			Status:      o.Status,
			CreatedAt:   o.CreatedAt,
			UpdatedAt:   o.UpdatedAt,
		})
	}
	return result, nil
}

// CountOrders 统计订单总数
func (r *orderRepository) CountOrders() (int64, error) {
	ctx := context.Background()
	return r.q.CountOrders(ctx)
}

// CountOrdersByUserID 统计用户订单总数
func (r *orderRepository) CountOrdersByUserID(userID string) (int64, error) {
	ctx := context.Background()
	return r.q.CountOrdersByUserID(ctx, userID)
}
