package database

import (
	"database/sql"
	"fmt"
	"time"

	"pay-mall/services/order-service/internal/config"

	_ "github.com/go-sql-driver/mysql"
)

// NewConnection 创建数据库连接
func NewConnection(cfg *config.Config) (*sql.DB, error) {
	dsn := cfg.GetDSN()
	
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)
	db.SetConnMaxLifetime(5 * time.Minute)

	// 测试连接
	if err := db.<PERSON>(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}
