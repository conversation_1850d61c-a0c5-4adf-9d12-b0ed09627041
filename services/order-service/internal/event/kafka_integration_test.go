package event

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"pay-mall/pkg/kafka"

	kafkago "github.com/segmentio/kafka-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	testKafkaBroker = "localhost:9092"
	testKafkaTopic  = "order-service-test-topic"
	testGroupID     = "order-service-test-group"
)

// setupKafkaTestTopic 创建测试主题
func setupKafkaTestTopic(t *testing.T) {
	conn, err := kafkago.Dial("tcp", testKafkaBroker)
	require.NoError(t, err)
	defer conn.Close()

	controller, err := conn.Controller()
	require.NoError(t, err)

	controllerConn, err := kafkago.Dial("tcp", fmt.Sprintf("%s:%d", controller.Host, controller.Port))
	require.NoError(t, err)
	defer controllerConn.Close()

	topicConfigs := []kafkago.TopicConfig{
		{
			Topic:             testKafkaTopic,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
	}

	err = controllerConn.CreateTopics(topicConfigs...)
	if err != nil {
		t.Logf("Topic creation failed (may already exist): %v", err)
	}
}

// cleanupKafkaTestTopic 清理测试主题
func cleanupKafkaTestTopic(t *testing.T) {
	conn, err := kafkago.Dial("tcp", testKafkaBroker)
	if err != nil {
		t.Logf("Failed to connect for cleanup: %v", err)
		return
	}
	defer conn.Close()

	controller, err := conn.Controller()
	if err != nil {
		t.Logf("Failed to get controller for cleanup: %v", err)
		return
	}

	controllerConn, err := kafkago.Dial("tcp", fmt.Sprintf("%s:%d", controller.Host, controller.Port))
	if err != nil {
		t.Logf("Failed to connect to controller for cleanup: %v", err)
		return
	}
	defer controllerConn.Close()

	err = controllerConn.DeleteTopics(testKafkaTopic)
	if err != nil {
		t.Logf("Failed to delete topic: %v", err)
	}
}

// TestKafkaOrderEventPublisherIntegration 测试订单事件发布器集成
func TestKafkaOrderEventPublisherIntegration(t *testing.T) {
	setupKafkaTestTopic(t)
	defer cleanupKafkaTestTopic(t)

	// 创建EventPublisher
	publisher, err := kafka.NewKafkaEventPublisher([]string{testKafkaBroker}, testKafkaTopic, "order-service")
	require.NoError(t, err)
	defer publisher.Close()

	// 创建Consumer用于验证（在发布前创建，从头开始读取）
	consumerConfig := kafka.ConsumerConfig{
		Brokers: []string{testKafkaBroker},
		Topic:   testKafkaTopic,
		GroupID: testGroupID,
	}
	consumer := kafka.NewConsumer(consumerConfig)
	defer consumer.Close()

	// 发布订单事件
	eventType := "order.created"
	orderData := map[string]interface{}{
		"order_id":     "order-123",
		"user_id":      "user-456",
		"total_amount": 99.99,
		"items": []interface{}{
			map[string]interface{}{
				"product_id": "prod-001",
				"quantity":   float64(2),
				"price":      49.99,
			},
		},
	}

	err = publisher.PublishOrderEvent(eventType, orderData)
	require.NoError(t, err)

	// 消费并验证消息 - 减少超时时间到2秒
	readCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	message, err := consumer.ReadMessage(readCtx)
	require.NoError(t, err)

	// 验证分区键
	assert.Equal(t, "order-order-123", string(message.Key))

	// 验证消息内容
	var eventMessage kafka.EventMessage
	err = json.Unmarshal(message.Value, &eventMessage)
	require.NoError(t, err)

	assert.Equal(t, eventType, eventMessage.Type)
	assert.Equal(t, "order-service", eventMessage.Source)
	assert.Equal(t, "1.0", eventMessage.Version)
	assert.NotEmpty(t, eventMessage.ID)
	assert.NotZero(t, eventMessage.Timestamp)

	// 验证事件数据
	assert.Equal(t, orderData["order_id"], eventMessage.Data["order_id"])
	assert.Equal(t, orderData["user_id"], eventMessage.Data["user_id"])
	assert.Equal(t, orderData["total_amount"], eventMessage.Data["total_amount"])
}
