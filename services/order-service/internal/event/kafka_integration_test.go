package event

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"pay-mall/pkg/kafka"

	"github.com/segmentio/kafka-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	testKafkaBroker = "localhost:9092"
	testKafkaTopic  = "order-service-test-topic"
	testGroupID     = "order-service-test-group"
)

// setupKafkaTestTopic 创建测试主题
func setupKafkaTestTopic(t *testing.T) {
	conn, err := kafka.Dial("tcp", testKafkaBroker)
	require.NoError(t, err)
	defer conn.Close()

	controller, err := conn.Controller()
	require.NoError(t, err)

	controllerConn, err := kafka.Dial("tcp", fmt.Sprintf("%s:%d", controller.Host, controller.Port))
	require.NoError(t, err)
	defer controllerConn.Close()

	topicConfigs := []kafka.TopicConfig{
		{
			Topic:             testKafkaTopic,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
	}

	err = controllerConn.CreateTopics(topicConfigs...)
	if err != nil {
		t.Logf("Topic creation warning: %v", err)
	}
}

// cleanupKafkaTestTopic 清理测试主题
func cleanupKafkaTestTopic(t *testing.T) {
	conn, err := kafka.Dial("tcp", testKafkaBroker)
	if err != nil {
		t.Logf("Failed to connect for cleanup: %v", err)
		return
	}
	defer conn.Close()

	controller, err := conn.Controller()
	if err != nil {
		t.Logf("Failed to get controller for cleanup: %v", err)
		return
	}

	controllerConn, err := kafka.Dial("tcp", fmt.Sprintf("%s:%d", controller.Host, controller.Port))
	if err != nil {
		t.Logf("Failed to connect to controller for cleanup: %v", err)
		return
	}
	defer controllerConn.Close()

	err = controllerConn.DeleteTopics(testKafkaTopic)
	if err != nil {
		t.Logf("Failed to delete topic: %v", err)
	}
}

// TestKafkaEventPublisherAdapter_OrderEvents 测试订单事件发布
func TestKafkaEventPublisherAdapter_OrderEvents(t *testing.T) {
	setupKafkaTestTopic(t)
	defer cleanupKafkaTestTopic(t)

	// 创建Kafka事件发布器
	kafkaPublisher, err := kafka.NewKafkaEventPublisher(
		[]string{testKafkaBroker},
		testKafkaTopic,
		"order-service",
	)
	require.NoError(t, err)

	// 创建适配器
	adapter := NewKafkaEventPublisherAdapter(kafkaPublisher)

	// 创建消费者
	consumer, err := kafka.NewConsumer(
		[]string{testKafkaBroker},
		testKafkaTopic,
		testGroupID,
	)
	require.NoError(t, err)

	// 创建测试订单事件
	orderEvent := CreateOrderCreatedEvent(
		"order-123",
		"user-456",
		199.99,
		[]OrderCreatedEventItem{
			{
				ProductID: "prod-001",
				Quantity:  2,
				Price:     99.99,
			},
		},
	)

	// 发布事件
	err = adapter.Publish(orderEvent)
	require.NoError(t, err)

	// 等待消息传播
	time.Sleep(2 * time.Second)

	// 消费并验证消息
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	message, err := consumer.ReadMessage(ctx)
	require.NoError(t, err)

	// 验证分区键
	assert.Equal(t, "order-order-123", string(message.Key))

	// 验证消息内容
	var receivedEvent kafka.Event
	err = json.Unmarshal(message.Value, &receivedEvent)
	require.NoError(t, err)

	assert.Equal(t, "order.created", receivedEvent.Type)
	assert.Equal(t, "order-service", receivedEvent.Source)
	assert.Equal(t, "1.0", receivedEvent.Version)

	// 验证事件数据
	data, ok := receivedEvent.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "order-123", data["order_id"])
	assert.Equal(t, "user-456", data["user_id"])
	assert.Equal(t, 199.99, data["total_amount"])

	// 验证订单项
	items, ok := data["items"].([]interface{})
	require.True(t, ok)
	assert.Len(t, items, 1)

	item, ok := items[0].(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "prod-001", item["product_id"])
	assert.Equal(t, float64(2), item["quantity"])
	assert.Equal(t, 99.99, item["price"])
}

// TestOrderEventTypes_Integration 测试所有订单事件类型
func TestOrderEventTypes_Integration(t *testing.T) {
	setupKafkaTestTopic(t)
	defer cleanupKafkaTestTopic(t)

	// 创建Kafka事件发布器
	kafkaPublisher, err := kafka.NewKafkaEventPublisher(
		[]string{testKafkaBroker},
		testKafkaTopic,
		"order-service",
	)
	require.NoError(t, err)

	adapter := NewKafkaEventPublisherAdapter(kafkaPublisher)

	// 创建消费者
	consumer, err := kafka.NewConsumer(
		[]string{testKafkaBroker},
		testKafkaTopic,
		fmt.Sprintf("%s-all-events", testGroupID),
	)
	require.NoError(t, err)

	// 测试事件列表
	testEvents := []struct {
		name  string
		event Event
	}{
		{
			name: "OrderCreated",
			event: CreateOrderCreatedEvent(
				"order-001",
				"user-001",
				99.99,
				[]OrderCreatedEventItem{
					{ProductID: "prod-001", Quantity: 1, Price: 99.99},
				},
			),
		},
		{
			name:  "OrderUpdated",
			event: CreateOrderUpdatedEvent("order-002", "confirmed"),
		},
		{
			name:  "OrderCancelled",
			event: CreateOrderCancelledEvent("order-003", "user requested"),
		},
	}

	// 发布所有事件
	for _, testEvent := range testEvents {
		err = adapter.Publish(testEvent.event)
		require.NoError(t, err, "Failed to publish %s event", testEvent.name)
	}

	// 等待消息传播
	time.Sleep(3 * time.Second)

	// 消费并验证所有消息
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	receivedEvents := make([]kafka.Event, 0, len(testEvents))
	for i := 0; i < len(testEvents); i++ {
		message, err := consumer.ReadMessage(ctx)
		require.NoError(t, err, "Failed to read message %d", i)

		var event kafka.Event
		err = json.Unmarshal(message.Value, &event)
		require.NoError(t, err, "Failed to unmarshal message %d", i)

		receivedEvents = append(receivedEvents, event)
	}

	// 验证接收到的事件数量
	assert.Len(t, receivedEvents, len(testEvents))

	// 验证事件类型
	eventTypes := make(map[string]bool)
	for _, event := range receivedEvents {
		eventTypes[event.Type] = true
		assert.Equal(t, "order-service", event.Source)
		assert.Equal(t, "1.0", event.Version)
		assert.NotEmpty(t, event.ID)
		assert.False(t, event.Timestamp.IsZero())
	}

	// 验证所有预期的事件类型都被接收
	assert.True(t, eventTypes["order.created"], "order.created event not received")
	assert.True(t, eventTypes["order.updated"], "order.updated event not received")
	assert.True(t, eventTypes["order.cancelled"], "order.cancelled event not received")
}

// TestOrderEventData_Validation 测试订单事件数据验证
func TestOrderEventData_Validation(t *testing.T) {
	setupKafkaTestTopic(t)
	defer cleanupKafkaTestTopic(t)

	kafkaPublisher, err := kafka.NewKafkaEventPublisher(
		[]string{testKafkaBroker},
		testKafkaTopic,
		"order-service",
	)
	require.NoError(t, err)

	adapter := NewKafkaEventPublisherAdapter(kafkaPublisher)

	consumer, err := kafka.NewConsumer(
		[]string{testKafkaBroker},
		testKafkaTopic,
		fmt.Sprintf("%s-validation", testGroupID),
	)
	require.NoError(t, err)

	// 创建复杂的订单事件
	orderItems := []OrderCreatedEventItem{
		{ProductID: "prod-001", Quantity: 2, Price: 49.99},
		{ProductID: "prod-002", Quantity: 1, Price: 99.99},
		{ProductID: "prod-003", Quantity: 3, Price: 29.99},
	}

	orderEvent := CreateOrderCreatedEvent(
		"order-complex-123",
		"user-789",
		289.95, // 2*49.99 + 1*99.99 + 3*29.99
		orderItems,
	)

	err = adapter.Publish(orderEvent)
	require.NoError(t, err)

	time.Sleep(2 * time.Second)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	message, err := consumer.ReadMessage(ctx)
	require.NoError(t, err)

	var receivedEvent kafka.Event
	err = json.Unmarshal(message.Value, &receivedEvent)
	require.NoError(t, err)

	// 验证订单数据完整性
	data, ok := receivedEvent.Data.(map[string]interface{})
	require.True(t, ok)

	assert.Equal(t, "order-complex-123", data["order_id"])
	assert.Equal(t, "user-789", data["user_id"])
	assert.Equal(t, 289.95, data["total_amount"])

	items, ok := data["items"].([]interface{})
	require.True(t, ok)
	assert.Len(t, items, 3)

	// 验证每个订单项
	expectedItems := []map[string]interface{}{
		{"product_id": "prod-001", "quantity": float64(2), "price": 49.99},
		{"product_id": "prod-002", "quantity": float64(1), "price": 99.99},
		{"product_id": "prod-003", "quantity": float64(3), "price": 29.99},
	}

	for i, expectedItem := range expectedItems {
		item, ok := items[i].(map[string]interface{})
		require.True(t, ok)
		assert.Equal(t, expectedItem["product_id"], item["product_id"])
		assert.Equal(t, expectedItem["quantity"], item["quantity"])
		assert.Equal(t, expectedItem["price"], item["price"])
	}
}

// TestOrderEventPartitioning 测试订单事件分区
func TestOrderEventPartitioning(t *testing.T) {
	setupKafkaTestTopic(t)
	defer cleanupKafkaTestTopic(t)

	kafkaPublisher, err := kafka.NewKafkaEventPublisher(
		[]string{testKafkaBroker},
		testKafkaTopic,
		"order-service",
	)
	require.NoError(t, err)

	adapter := NewKafkaEventPublisherAdapter(kafkaPublisher)

	consumer, err := kafka.NewConsumer(
		[]string{testKafkaBroker},
		testKafkaTopic,
		fmt.Sprintf("%s-partitioning", testGroupID),
	)
	require.NoError(t, err)

	// 发布多个订单事件，测试分区键
	orderIDs := []string{"order-001", "order-002", "order-003", "order-004", "order-005"}

	for _, orderID := range orderIDs {
		orderEvent := CreateOrderCreatedEvent(
			orderID,
			"user-123",
			99.99,
			[]OrderCreatedEventItem{
				{ProductID: "prod-001", Quantity: 1, Price: 99.99},
			},
		)

		err = adapter.Publish(orderEvent)
		require.NoError(t, err)
	}

	time.Sleep(3 * time.Second)

	// 验证分区键
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	receivedKeys := make([]string, 0, len(orderIDs))
	for i := 0; i < len(orderIDs); i++ {
		message, err := consumer.ReadMessage(ctx)
		require.NoError(t, err)

		receivedKeys = append(receivedKeys, string(message.Key))
	}

	// 验证所有分区键都是正确的格式
	for i, orderID := range orderIDs {
		expectedKey := fmt.Sprintf("order-%s", orderID)
		assert.Contains(t, receivedKeys, expectedKey, "Missing partition key for order %s", orderID)
	}
}
