package event

import (
	"encoding/json"
	"fmt"
	"time"
)

// EventPublisher 事件发布器接口
type EventPublisher interface {
	Publish(event Event) error
}

// Event 事件接口
type Event interface {
	GetEventType() string
	GetEventData() interface{}
	GetTimestamp() time.Time
}

// BaseEvent 基础事件结构
type BaseEvent struct {
	EventType string      `json:"event_type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

func (e BaseEvent) GetEventType() string {
	return e.EventType
}

func (e BaseEvent) GetEventData() interface{} {
	return e.Data
}

func (e BaseEvent) GetTimestamp() time.Time {
	return e.Timestamp
}

// OrderCreatedEvent 订单创建事件
type OrderCreatedEvent struct {
	BaseEvent
}

// OrderCreatedEventData 订单创建事件数据
type OrderCreatedEventData struct {
	OrderID     string                  `json:"order_id"`
	UserID      string                  `json:"user_id"`
	TotalAmount float64                 `json:"total_amount"`
	Items       []OrderCreatedEventItem `json:"items"`
	Timestamp   time.Time               `json:"timestamp"`
}

// OrderCreatedEventItem 订单创建事件项
type OrderCreatedEventItem struct {
	ProductID string  `json:"product_id"`
	Quantity  int     `json:"quantity"`
	Price     float64 `json:"price"`
}

// OrderPaidEvent 订单支付成功事件
type OrderPaidEvent struct {
	BaseEvent
}

// OrderPaidEventData 订单支付成功事件数据
type OrderPaidEventData struct {
	OrderID   string    `json:"order_id"`
	UserID    string    `json:"user_id"`
	Amount    float64   `json:"amount"`
	Timestamp time.Time `json:"timestamp"`
}

// OrderCancelledEvent 订单取消事件
type OrderCancelledEvent struct {
	BaseEvent
}

// OrderCancelledEventData 订单取消事件数据
type OrderCancelledEventData struct {
	OrderID   string    `json:"order_id"`
	UserID    string    `json:"user_id"`
	Reason    string    `json:"reason"`
	Timestamp time.Time `json:"timestamp"`
}

// CreateOrderCreatedEvent 创建订单创建事件
func CreateOrderCreatedEvent(orderID, userID string, totalAmount float64, items []OrderCreatedEventItem) Event {
	return OrderCreatedEvent{
		BaseEvent: BaseEvent{
			EventType: "OrderCreated",
			Data: OrderCreatedEventData{
				OrderID:     orderID,
				UserID:      userID,
				TotalAmount: totalAmount,
				Items:       items,
				Timestamp:   time.Now(),
			},
			Timestamp: time.Now(),
		},
	}
}

// CreateOrderPaidEvent 创建订单支付成功事件
func CreateOrderPaidEvent(orderID, userID string, amount float64) Event {
	return OrderPaidEvent{
		BaseEvent: BaseEvent{
			EventType: "OrderPaid",
			Data: OrderPaidEventData{
				OrderID:   orderID,
				UserID:    userID,
				Amount:    amount,
				Timestamp: time.Now(),
			},
			Timestamp: time.Now(),
		},
	}
}

// CreateOrderCancelledEvent 创建订单取消事件
func CreateOrderCancelledEvent(orderID, userID, reason string) Event {
	return OrderCancelledEvent{
		BaseEvent: BaseEvent{
			EventType: "OrderCancelled",
			Data: OrderCancelledEventData{
				OrderID:   orderID,
				UserID:    userID,
				Reason:    reason,
				Timestamp: time.Now(),
			},
			Timestamp: time.Now(),
		},
	}
}

// MockEventPublisher 模拟事件发布器
type MockEventPublisher struct{}

// NewMockEventPublisher 创建模拟事件发布器
func NewMockEventPublisher() EventPublisher {
	return &MockEventPublisher{}
}

// Publish 发布事件（模拟实现）
func (p *MockEventPublisher) Publish(event Event) error {
	eventData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	fmt.Printf("Published event: %s - %s\n", event.GetEventType(), string(eventData))
	return nil
}

// KafkaEventPublisherAdapter Kafka事件发布器适配器
type KafkaEventPublisherAdapter struct {
	kafkaPublisher interface {
		PublishOrderEvent(eventType string, data map[string]interface{}) error
	}
}

// NewKafkaEventPublisherAdapter 创建Kafka事件发布器适配器
func NewKafkaEventPublisherAdapter(kafkaPublisher interface {
	PublishOrderEvent(eventType string, data map[string]interface{}) error
}) EventPublisher {
	return &KafkaEventPublisherAdapter{
		kafkaPublisher: kafkaPublisher,
	}
}

// Publish 发布事件到Kafka
func (k *KafkaEventPublisherAdapter) Publish(event Event) error {
	// 将事件数据转换为map[string]interface{}
	eventData := map[string]interface{}{
		"event_type": event.GetEventType(),
		"data":       event.GetEventData(),
		"timestamp":  event.GetTimestamp(),
	}

	return k.kafkaPublisher.PublishOrderEvent(event.GetEventType(), eventData)
}
