package handler_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"pay-mall/services/order-service/internal/handler"
	"pay-mall/services/order-service/internal/model"

	"github.com/gin-gonic/gin"
)

// MockOrderService 是 service.OrderService 接口的模拟实现
type MockOrderService struct {
	T *testing.T

	CreateOrderFunc       func(req *model.CreateOrderRequest) (*model.OrderResponse, error)
	GetOrderByIDFunc      func(id string) (*model.OrderResponse, error)
	UpdateOrderStatusFunc func(id string, req *model.UpdateOrderStatusRequest) (*model.OrderResponse, error)
	CancelOrderFunc       func(id, reason string) error
	ListOrdersFunc        func(page, pageSize int) (*model.OrderListResponse, error)
	GetOrdersByUserIDFunc func(userID string, page, pageSize int) (*model.OrderListResponse, error)
}

func (m *MockOrderService) CreateOrder(req *model.CreateOrderRequest) (*model.OrderResponse, error) {
	if m.CreateOrderFunc != nil {
		return m.CreateOrderFunc(req)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("CreateOrder not mocked, but was called")
	}
	return nil, errors.New("CreateOrder not mocked")
}

func (m *MockOrderService) GetOrderByID(id string) (*model.OrderResponse, error) {
	if m.GetOrderByIDFunc != nil {
		return m.GetOrderByIDFunc(id)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("GetOrderByID not mocked, but was called")
	}
	return nil, errors.New("GetOrderByID not mocked")
}

func (m *MockOrderService) UpdateOrderStatus(id string, req *model.UpdateOrderStatusRequest) (*model.OrderResponse, error) {
	if m.UpdateOrderStatusFunc != nil {
		return m.UpdateOrderStatusFunc(id, req)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("UpdateOrderStatus not mocked, but was called")
	}
	return nil, errors.New("UpdateOrderStatus not mocked")
}

func (m *MockOrderService) CancelOrder(id, reason string) error {
	if m.CancelOrderFunc != nil {
		return m.CancelOrderFunc(id, reason)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("CancelOrder not mocked, but was called")
	}
	return errors.New("CancelOrder not mocked")
}

func (m *MockOrderService) ListOrders(page, pageSize int) (*model.OrderListResponse, error) {
	if m.ListOrdersFunc != nil {
		return m.ListOrdersFunc(page, pageSize)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("ListOrders not mocked, but was called")
	}
	return nil, errors.New("ListOrders not mocked")
}

func (m *MockOrderService) GetOrdersByUserID(userID string, page, pageSize int) (*model.OrderListResponse, error) {
	if m.GetOrdersByUserIDFunc != nil {
		return m.GetOrdersByUserIDFunc(userID, page, pageSize)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("GetOrdersByUserID not mocked, but was called")
	}
	return nil, errors.New("GetOrdersByUserID not mocked")
}

// setupRouter 设置 Gin 路由器并注入模拟依赖
func setupRouter(mockOrderService *MockOrderService) *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.Default()

	orderHandler := handler.NewOrderHandler(mockOrderService)

	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "order-service",
		})
	})

	v1 := r.Group("/api/v1")
	{
		orders := v1.Group("/orders")
		{
			orders.POST("", orderHandler.CreateOrder)
			orders.GET("", orderHandler.ListOrders)
			orders.GET("/:id", orderHandler.GetOrder)
			orders.PUT("/:id/status", orderHandler.UpdateOrderStatus)
			orders.DELETE("/:id", orderHandler.CancelOrder)
		}

		users := v1.Group("/users")
		{
			users.GET("/:user_id/orders", orderHandler.GetUserOrders)
		}
	}
	return r
}

func TestHealthCheck(t *testing.T) {
	mockOrderService := &MockOrderService{}
	router := setupRouter(mockOrderService)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/health", nil)
	router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if status, ok := response["status"].(string); !ok || status != "healthy" {
		t.Errorf("Expected status 'healthy', got %v", response["status"])
	}
}

func TestCreateOrder(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    model.CreateOrderRequest
		mockFunc       func(req *model.CreateOrderRequest) (*model.OrderResponse, error)
		expectedStatus int
		expectedBody   map[string]interface{}
	}{
		{
			name: "成功创建订单",
			requestBody: model.CreateOrderRequest{
				UserID: "user-123",
				Items: []model.CreateOrderItem{
					{
						ProductID: "product-1",
						Quantity:  2,
						Price:     99.99,
					},
				},
			},
			mockFunc: func(req *model.CreateOrderRequest) (*model.OrderResponse, error) {
				return &model.OrderResponse{
					ID:          "order-123",
					UserID:      req.UserID,
					TotalAmount: 199.98,
					Status:      model.OrderStatusPending,
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				}, nil
			},
			expectedStatus: http.StatusCreated,
			expectedBody: map[string]interface{}{
				"message": "Order created successfully",
			},
		},
		{
			name: "无效请求数据",
			requestBody: model.CreateOrderRequest{
				UserID: "",
				Items:  []model.CreateOrderItem{},
			},
			mockFunc:       nil,
			expectedStatus: http.StatusBadRequest,
			expectedBody: map[string]interface{}{
				"error": "Invalid request data",
			},
		},
		{
			name: "创建订单失败",
			requestBody: model.CreateOrderRequest{
				UserID: "user-123",
				Items: []model.CreateOrderItem{
					{
						ProductID: "product-1",
						Quantity:  2,
						Price:     99.99,
					},
				},
			},
			mockFunc: func(req *model.CreateOrderRequest) (*model.OrderResponse, error) {
				return nil, errors.New("failed to create order")
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"error": "Failed to create order",
			},
		},
	}

	for _, tt := range tests {
		testCase := tt
		t.Run(testCase.name, func(t *testing.T) {
			mockOrderService := &MockOrderService{
				T: t,
			}

			if testCase.mockFunc != nil {
				mockOrderService.CreateOrderFunc = testCase.mockFunc
			}

			router := setupRouter(mockOrderService)

			jsonBody, _ := json.Marshal(testCase.requestBody)
			w := httptest.NewRecorder()
			req, _ := http.NewRequest("POST", "/api/v1/orders", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")
			router.ServeHTTP(w, req)

			if w.Code != testCase.expectedStatus {
				t.Errorf("Expected status %d, got %d. Body: %s", testCase.expectedStatus, w.Code, w.Body.String())
			}

			var actualBody map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &actualBody)
			if err != nil {
				t.Fatalf("Failed to unmarshal actual response body: %v", err)
			}

			// 比较 message 或 error 字段
			if expectedMsg, ok := testCase.expectedBody["message"]; ok {
				if actualMsg, ok := actualBody["message"]; !ok || actualMsg != expectedMsg {
					t.Errorf("Expected message %q, got %q", expectedMsg, actualMsg)
				}
			}
			if expectedErr, ok := testCase.expectedBody["error"]; ok {
				if actualErr, ok := actualBody["error"]; !ok || actualErr != expectedErr {
					t.Errorf("Expected error %q, got %q", expectedErr, actualErr)
				}
			}
		})
	}
}

func TestGetOrder(t *testing.T) {
	tests := []struct {
		name           string
		orderID        string
		mockFunc       func(id string) (*model.OrderResponse, error)
		expectedStatus int
		expectedBody   map[string]interface{}
	}{
		{
			name:    "成功获取订单",
			orderID: "order-123",
			mockFunc: func(id string) (*model.OrderResponse, error) {
				return &model.OrderResponse{
					ID:          id,
					UserID:      "user-123",
					TotalAmount: 199.98,
					Status:      model.OrderStatusPending,
					Items: []model.OrderItemResponse{
						{
							ID:        "item-1",
							ProductID: "product-1",
							Quantity:  2,
							Price:     99.99,
						},
					},
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil
			},
			expectedStatus: http.StatusOK,
			expectedBody: map[string]interface{}{
				"order": map[string]interface{}{
					"id":      "order-123",
					"user_id": "user-123",
					"status":  model.OrderStatusPending,
				},
			},
		},
		{
			name:    "订单未找到",
			orderID: "order-404",
			mockFunc: func(id string) (*model.OrderResponse, error) {
				return nil, errors.New("order not found")
			},
			expectedStatus: http.StatusNotFound,
			expectedBody: map[string]interface{}{
				"error": "Order not found",
			},
		},
	}

	for _, tt := range tests {
		testCase := tt
		t.Run(testCase.name, func(t *testing.T) {
			mockOrderService := &MockOrderService{
				T: t,
			}

			if testCase.mockFunc != nil {
				mockOrderService.GetOrderByIDFunc = testCase.mockFunc
			}

			router := setupRouter(mockOrderService)

			w := httptest.NewRecorder()
			req, _ := http.NewRequest("GET", "/api/v1/orders/"+testCase.orderID, nil)
			router.ServeHTTP(w, req)

			if w.Code != testCase.expectedStatus {
				t.Errorf("Expected status %d, got %d. Body: %s", testCase.expectedStatus, w.Code, w.Body.String())
			}
		})
	}
}
