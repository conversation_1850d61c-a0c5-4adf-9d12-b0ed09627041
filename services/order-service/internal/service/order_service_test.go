package service

import (
	"testing"

	"pay-mall/services/order-service/internal/event"
	"pay-mall/services/order-service/internal/model"
)

// MockOrderRepository 模拟订单仓库
type MockOrderRepository struct {
	orders     map[string]*model.Order
	orderItems map[string][]*model.OrderItem
}

func NewMockOrderRepository() *MockOrderRepository {
	return &MockOrderRepository{
		orders:     make(map[string]*model.Order),
		orderItems: make(map[string][]*model.OrderItem),
	}
}

func (m *MockOrderRepository) Create(order *model.Order) error {
	m.orders[order.ID] = order
	return nil
}

func (m *MockOrderRepository) CreateOrderItem(item *model.OrderItem) error {
	if m.orderItems[item.OrderID] == nil {
		m.orderItems[item.OrderID] = make([]*model.OrderItem, 0)
	}
	m.orderItems[item.OrderID] = append(m.orderItems[item.OrderID], item)
	return nil
}

func (m *MockOrderRepository) FindByID(id string) (*model.Order, error) {
	if order, exists := m.orders[id]; exists {
		return order, nil
	}
	return nil, nil
}

func (m *MockOrderRepository) FindByUserID(userID string, limit, offset int32) ([]*model.Order, error) {
	var orders []*model.Order
	for _, order := range m.orders {
		if order.UserID == userID {
			orders = append(orders, order)
		}
	}
	return orders, nil
}

func (m *MockOrderRepository) FindItemsByOrderID(orderID string) ([]*model.OrderItem, error) {
	if items, exists := m.orderItems[orderID]; exists {
		return items, nil
	}
	return []*model.OrderItem{}, nil
}

func (m *MockOrderRepository) UpdateStatus(id string, status string) error {
	if order, exists := m.orders[id]; exists {
		order.Status = status
		return nil
	}
	return nil
}

func (m *MockOrderRepository) CountOrders() (int64, error) {
	return int64(len(m.orders)), nil
}

func (m *MockOrderRepository) CountOrdersByUserID(userID string) (int64, error) {
	count := int64(0)
	for _, order := range m.orders {
		if order.UserID == userID {
			count++
		}
	}
	return count, nil
}

func (m *MockOrderRepository) ListOrders(limit, offset int32) ([]*model.Order, error) {
	var orders []*model.Order
	count := 0
	for _, order := range m.orders {
		if count >= int(offset) && len(orders) < int(limit) {
			orders = append(orders, order)
		}
		count++
	}
	return orders, nil
}

func (m *MockOrderRepository) Delete(id string) error {
	delete(m.orders, id)
	delete(m.orderItems, id)
	return nil
}

func (m *MockOrderRepository) DeleteItems(orderID string) error {
	delete(m.orderItems, orderID)
	return nil
}

func TestOrderService_CreateOrder(t *testing.T) {
	// 创建模拟依赖
	mockRepo := NewMockOrderRepository()
	mockEventPublisher := event.NewMockEventPublisher()

	// 创建服务实例
	service := NewOrderService(mockRepo, mockEventPublisher)

	// 测试用例
	tests := []struct {
		name    string
		req     *model.CreateOrderRequest
		wantErr bool
	}{
		{
			name: "valid order creation",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items: []model.CreateOrderItem{
					{
						ProductID: "product-1",
						Quantity:  2,
						Price:     99.99,
					},
					{
						ProductID: "product-2",
						Quantity:  1,
						Price:     49.99,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "empty items",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items:  []model.CreateOrderItem{},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := service.CreateOrder(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestOrderService_GetOrderByID(t *testing.T) {
	// 创建模拟依赖
	mockRepo := NewMockOrderRepository()
	mockEventPublisher := event.NewMockEventPublisher()

	// 创建服务实例
	service := NewOrderService(mockRepo, mockEventPublisher)

	// 先创建一个订单
	createReq := &model.CreateOrderRequest{
		UserID: "user-123",
		Items: []model.CreateOrderItem{
			{
				ProductID: "product-1",
				Quantity:  2,
				Price:     99.99,
			},
		},
	}
	orderResp, err := service.CreateOrder(createReq)
	if err != nil {
		t.Fatalf("Failed to create order for test: %v", err)
	}

	// 测试获取订单
	foundOrder, err := service.GetOrderByID(orderResp.ID)
	if err != nil {
		t.Errorf("GetOrderByID() error = %v", err)
	}
	if foundOrder == nil {
		t.Error("Expected order to be found, got nil")
	}

	// 测试获取不存在的订单
	_, err = service.GetOrderByID("non-existing-id")
	if err == nil {
		t.Error("Expected error for non-existing order, got nil")
	}
}
