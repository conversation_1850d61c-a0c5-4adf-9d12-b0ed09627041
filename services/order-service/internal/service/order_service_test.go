package service

import (
	"errors"
	"testing"
	"time"

	"pay-mall/services/order-service/internal/event"
	"pay-mall/services/order-service/internal/model"
)

// MockOrderRepository 真正的Mock实现 - 使用函数指针控制行为
type MockOrderRepository struct {
	T *testing.T

	// 函数指针，允许每个测试用例自定义行为
	CreateFunc              func(order *model.Order) error
	CreateOrderItemFunc     func(item *model.OrderItem) error
	FindByIDFunc            func(id string) (*model.Order, error)
	FindByUserIDFunc        func(userID string, limit, offset int32) ([]*model.Order, error)
	FindItemsByOrderIDFunc  func(orderID string) ([]*model.OrderItem, error)
	UpdateStatusFunc        func(id string, status string) error
	CountOrdersFunc         func() (int64, error)
	CountOrdersByUserIDFunc func(userID string) (int64, error)
	ListOrdersFunc          func(limit, offset int32) ([]*model.Order, error)
	DeleteFunc              func(id string) error
	DeleteItemsFunc         func(orderID string) error
}

func NewMockOrderRepository(t *testing.T) *MockOrderRepository {
	return &MockOrderRepository{T: t}
}

func (m *MockOrderRepository) Create(order *model.Order) error {
	if m.CreateFunc != nil {
		return m.CreateFunc(order)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("Create not mocked, but was called")
	}
	return nil
}

func (m *MockOrderRepository) CreateOrderItem(item *model.OrderItem) error {
	if m.CreateOrderItemFunc != nil {
		return m.CreateOrderItemFunc(item)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("CreateOrderItem not mocked, but was called")
	}
	return nil
}

func (m *MockOrderRepository) FindByID(id string) (*model.Order, error) {
	if m.FindByIDFunc != nil {
		return m.FindByIDFunc(id)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("FindByID not mocked, but was called")
	}
	return nil, nil
}

func (m *MockOrderRepository) FindByUserID(userID string, limit, offset int32) ([]*model.Order, error) {
	if m.FindByUserIDFunc != nil {
		return m.FindByUserIDFunc(userID, limit, offset)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("FindByUserID not mocked, but was called")
	}
	return nil, nil
}

func (m *MockOrderRepository) FindItemsByOrderID(orderID string) ([]*model.OrderItem, error) {
	if m.FindItemsByOrderIDFunc != nil {
		return m.FindItemsByOrderIDFunc(orderID)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("FindItemsByOrderID not mocked, but was called")
	}
	return nil, nil
}

func (m *MockOrderRepository) UpdateStatus(id string, status string) error {
	if m.UpdateStatusFunc != nil {
		return m.UpdateStatusFunc(id, status)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("UpdateStatus not mocked, but was called")
	}
	return nil
}

func (m *MockOrderRepository) CountOrders() (int64, error) {
	if m.CountOrdersFunc != nil {
		return m.CountOrdersFunc()
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("CountOrders not mocked, but was called")
	}
	return 0, nil
}

func (m *MockOrderRepository) CountOrdersByUserID(userID string) (int64, error) {
	if m.CountOrdersByUserIDFunc != nil {
		return m.CountOrdersByUserIDFunc(userID)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("CountOrdersByUserID not mocked, but was called")
	}
	return 0, nil
}

func (m *MockOrderRepository) ListOrders(limit, offset int32) ([]*model.Order, error) {
	if m.ListOrdersFunc != nil {
		return m.ListOrdersFunc(limit, offset)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("ListOrders not mocked, but was called")
	}
	return nil, nil
}

func (m *MockOrderRepository) Delete(id string) error {
	if m.DeleteFunc != nil {
		return m.DeleteFunc(id)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("Delete not mocked, but was called")
	}
	return nil
}

func (m *MockOrderRepository) DeleteItems(orderID string) error {
	if m.DeleteItemsFunc != nil {
		return m.DeleteItemsFunc(orderID)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("DeleteItems not mocked, but was called")
	}
	return nil
}

func TestOrderService_CreateOrder(t *testing.T) {
	tests := []struct {
		name      string
		req       *model.CreateOrderRequest
		setupMock func(*MockOrderRepository)
		wantErr   bool
	}{
		{
			name: "valid order creation",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items: []model.CreateOrderItem{
					{
						ProductID: "product-1",
						Quantity:  2,
						Price:     99.99,
					},
					{
						ProductID: "product-2",
						Quantity:  1,
						Price:     49.99,
					},
				},
			},
			setupMock: func(mockRepo *MockOrderRepository) {
				// Mock成功创建订单
				mockRepo.CreateFunc = func(order *model.Order) error {
					return nil
				}
				// Mock成功创建订单项
				mockRepo.CreateOrderItemFunc = func(item *model.OrderItem) error {
					return nil
				}
			},
			wantErr: false,
		},
		{
			name: "empty items",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items:  []model.CreateOrderItem{},
			},
			setupMock: func(mockRepo *MockOrderRepository) {
				// 空订单项的情况下，不应该调用repository方法
			},
			wantErr: true,
		},
		{
			name: "repository create error",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items: []model.CreateOrderItem{
					{
						ProductID: "product-1",
						Quantity:  1,
						Price:     99.99,
					},
				},
			},
			setupMock: func(mockRepo *MockOrderRepository) {
				// Mock创建订单失败
				mockRepo.CreateFunc = func(order *model.Order) error {
					return errors.New("database error")
				}
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模拟依赖
			mockRepo := NewMockOrderRepository(t)
			mockEventPublisher := event.NewMockEventPublisher()

			// 设置Mock行为
			if tt.setupMock != nil {
				tt.setupMock(mockRepo)
			}

			// 创建服务实例
			service := NewOrderService(mockRepo, mockEventPublisher)

			// 执行测试
			_, err := service.CreateOrder(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestOrderService_GetOrderByID(t *testing.T) {
	tests := []struct {
		name      string
		orderID   string
		setupMock func(*MockOrderRepository)
		wantErr   bool
	}{
		{
			name:    "existing order",
			orderID: "order-123",
			setupMock: func(mockRepo *MockOrderRepository) {
				// Mock找到订单
				mockRepo.FindByIDFunc = func(id string) (*model.Order, error) {
					if id == "order-123" {
						order := &model.Order{
							ID:          id,
							UserID:      "user-123",
							TotalAmount: 199.98,
							Status:      model.OrderStatusPending,
						}
						order.SetCreatedAt(time.Now())
						order.SetUpdatedAt(time.Now())
						return order, nil
					}
					return nil, nil
				}
				// Mock找到订单项
				mockRepo.FindItemsByOrderIDFunc = func(orderID string) ([]*model.OrderItem, error) {
					return []*model.OrderItem{
						{
							ID:        "item-1",
							OrderID:   orderID,
							ProductID: "product-1",
							Quantity:  2,
							Price:     99.99,
						},
					}, nil
				}
			},
			wantErr: false,
		},
		{
			name:    "non-existing order",
			orderID: "non-existing-id",
			setupMock: func(mockRepo *MockOrderRepository) {
				// Mock未找到订单
				mockRepo.FindByIDFunc = func(id string) (*model.Order, error) {
					return nil, nil
				}
			},
			wantErr: true,
		},
		{
			name:    "repository error",
			orderID: "error-id",
			setupMock: func(mockRepo *MockOrderRepository) {
				// Mock数据库错误
				mockRepo.FindByIDFunc = func(id string) (*model.Order, error) {
					return nil, errors.New("database connection error")
				}
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模拟依赖
			mockRepo := NewMockOrderRepository(t)
			mockEventPublisher := event.NewMockEventPublisher()

			// 设置Mock行为
			if tt.setupMock != nil {
				tt.setupMock(mockRepo)
			}

			// 创建服务实例
			service := NewOrderService(mockRepo, mockEventPublisher)

			// 执行测试
			result, err := service.GetOrderByID(tt.orderID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrderByID() error = %v, wantErr %v", err, tt.wantErr)
			}

			if !tt.wantErr && result == nil {
				t.Error("Expected order to be found, got nil")
			}
		})
	}
}
