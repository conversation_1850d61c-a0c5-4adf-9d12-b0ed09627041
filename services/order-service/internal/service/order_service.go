package service

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"pay-mall/services/order-service/internal/event"
	"pay-mall/services/order-service/internal/model"
	"pay-mall/services/order-service/internal/repository"
)

// OrderService 订单服务接口
type OrderService interface {
	CreateOrder(req *model.CreateOrderRequest) (*model.OrderResponse, error)
	GetOrderByID(id string) (*model.OrderResponse, error)
	GetOrdersByUserID(userID string, page, pageSize int) (*model.OrderListResponse, error)
	UpdateOrderStatus(id string, req *model.UpdateOrderStatusRequest) (*model.OrderResponse, error)
	CancelOrder(id, reason string) error
	ListOrders(page, pageSize int) (*model.OrderListResponse, error)
}

// orderService 订单服务实现
type orderService struct {
	orderRepo      repository.OrderRepository
	eventPublisher event.EventPublisher
}

// NewOrderService 创建订单服务实例
func NewOrderService(orderRepo repository.OrderRepository, eventPublisher event.EventPublisher) OrderService {
	return &orderService{
		orderRepo:      orderRepo,
		eventPublisher: eventPublisher,
	}
}

// CreateOrder 创建订单
func (s *orderService) CreateOrder(req *model.CreateOrderRequest) (*model.OrderResponse, error) {
	// 生成订单ID
	orderID, err := generateOrderID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate order ID: %w", err)
	}

	// 计算总金额
	var totalAmount float64
	for _, item := range req.Items {
		totalAmount += item.Price * float64(item.Quantity)
	}

	// 创建订单
	order := &model.Order{
		ID:          orderID,
		UserID:      req.UserID,
		TotalAmount: totalAmount,
		Status:      model.OrderStatusPending,
	}

	// 设置时间
	now := time.Now()
	order.SetCreatedAt(now)
	order.SetUpdatedAt(now)

	if err := s.orderRepo.Create(order); err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// 创建订单项
	var orderItems []model.OrderItemResponse
	for _, item := range req.Items {
		itemID, err := generateOrderItemID()
		if err != nil {
			return nil, fmt.Errorf("failed to generate order item ID: %w", err)
		}

		orderItem := &model.OrderItem{
			ID:        itemID,
			OrderID:   orderID,
			ProductID: item.ProductID,
			Quantity:  item.Quantity,
			Price:     item.Price,
		}

		if err := s.orderRepo.CreateOrderItem(orderItem); err != nil {
			return nil, fmt.Errorf("failed to create order item: %w", err)
		}

		orderItems = append(orderItems, model.OrderItemResponse{
			ID:        orderItem.ID,
			ProductID: orderItem.ProductID,
			Quantity:  orderItem.Quantity,
			Price:     orderItem.Price,
		})
	}

	// 发布订单创建事件
	var eventItems []event.OrderCreatedEventItem
	for _, item := range req.Items {
		eventItems = append(eventItems, event.OrderCreatedEventItem{
			ProductID: item.ProductID,
			Quantity:  item.Quantity,
			Price:     item.Price,
		})
	}

	orderCreatedEvent := event.CreateOrderCreatedEvent(orderID, req.UserID, totalAmount, eventItems)
	if err := s.eventPublisher.Publish(orderCreatedEvent); err != nil {
		// 记录错误但不影响订单创建流程
		fmt.Printf("Failed to publish order created event: %v\n", err)
	}

	response := order.ToOrderResponse()
	response.Items = orderItems
	return &response, nil
}

// GetOrderByID 根据ID获取订单
func (s *orderService) GetOrderByID(id string) (*model.OrderResponse, error) {
	order, err := s.orderRepo.FindByID(id)
	if err != nil {
		return nil, fmt.Errorf("order not found: %w", err)
	}

	// 获取订单项
	items, err := s.orderRepo.FindItemsByOrderID(id)
	if err != nil {
		return nil, fmt.Errorf("failed to get order items: %w", err)
	}

	var orderItems []model.OrderItemResponse
	for _, item := range items {
		orderItems = append(orderItems, model.OrderItemResponse{
			ID:        item.ID,
			ProductID: item.ProductID,
			Quantity:  item.Quantity,
			Price:     item.Price,
		})
	}

	response := order.ToOrderResponse()
	response.Items = orderItems
	return &response, nil
}

// GetOrdersByUserID 根据用户ID获取订单列表
func (s *orderService) GetOrdersByUserID(userID string, page, pageSize int) (*model.OrderListResponse, error) {
	// 参数校验
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 计算偏移量
	offset := int32((page - 1) * pageSize)
	limit := int32(pageSize)

	// 获取订单列表
	orders, err := s.orderRepo.FindByUserID(userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get orders: %w", err)
	}

	// 获取总数
	total, err := s.orderRepo.CountOrdersByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to count orders: %w", err)
	}

	// 转换为响应格式
	var orderResponses []model.OrderResponse
	for _, order := range orders {
		// 获取订单项
		items, err := s.orderRepo.FindItemsByOrderID(order.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get order items: %w", err)
		}

		var orderItems []model.OrderItemResponse
		for _, item := range items {
			orderItems = append(orderItems, model.OrderItemResponse{
				ID:        item.ID,
				ProductID: item.ProductID,
				Quantity:  item.Quantity,
				Price:     item.Price,
			})
		}

		response := order.ToOrderResponse()
		response.Items = orderItems
		orderResponses = append(orderResponses, response)
	}

	return &model.OrderListResponse{
		Orders:   orderResponses,
		Page:     page,
		PageSize: pageSize,
		Total:    total,
	}, nil
}

// UpdateOrderStatus 更新订单状态
func (s *orderService) UpdateOrderStatus(id string, req *model.UpdateOrderStatusRequest) (*model.OrderResponse, error) {
	// 检查订单是否存在
	existingOrder, err := s.orderRepo.FindByID(id)
	if err != nil {
		return nil, fmt.Errorf("order not found: %w", err)
	}

	// 更新订单状态
	if err := s.orderRepo.UpdateStatus(id, req.Status); err != nil {
		return nil, fmt.Errorf("failed to update order status: %w", err)
	}

	// 发布相应的事件
	switch req.Status {
	case model.OrderStatusPaid:
		orderPaidEvent := event.CreateOrderPaidEvent(id, existingOrder.UserID, existingOrder.TotalAmount)
		if err := s.eventPublisher.Publish(orderPaidEvent); err != nil {
			fmt.Printf("Failed to publish order paid event: %v\n", err)
		}
	}

	// 返回更新后的订单
	return s.GetOrderByID(id)
}

// CancelOrder 取消订单
func (s *orderService) CancelOrder(id, reason string) error {
	// 检查订单是否存在
	existingOrder, err := s.orderRepo.FindByID(id)
	if err != nil {
		return fmt.Errorf("order not found: %w", err)
	}

	// 更新订单状态为取消
	if err := s.orderRepo.UpdateStatus(id, model.OrderStatusCancelled); err != nil {
		return fmt.Errorf("failed to cancel order: %w", err)
	}

	// 发布订单取消事件
	orderCancelledEvent := event.CreateOrderCancelledEvent(id, existingOrder.UserID, reason)
	if err := s.eventPublisher.Publish(orderCancelledEvent); err != nil {
		fmt.Printf("Failed to publish order cancelled event: %v\n", err)
	}

	return nil
}

// ListOrders 获取订单列表
func (s *orderService) ListOrders(page, pageSize int) (*model.OrderListResponse, error) {
	// 参数校验
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 计算偏移量
	offset := int32((page - 1) * pageSize)
	limit := int32(pageSize)

	// 获取订单列表
	orders, err := s.orderRepo.ListOrders(limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list orders: %w", err)
	}

	// 获取总数
	total, err := s.orderRepo.CountOrders()
	if err != nil {
		return nil, fmt.Errorf("failed to count orders: %w", err)
	}

	// 转换为响应格式
	var orderResponses []model.OrderResponse
	for _, order := range orders {
		response := order.ToOrderResponse()
		orderResponses = append(orderResponses, response)
	}

	return &model.OrderListResponse{
		Orders:   orderResponses,
		Page:     page,
		PageSize: pageSize,
		Total:    total,
	}, nil
}

// generateOrderID 生成订单ID
func generateOrderID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generateOrderItemID 生成订单项ID
func generateOrderItemID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
