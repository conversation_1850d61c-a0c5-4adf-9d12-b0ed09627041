package service

import (
	"errors"
	"testing"
	"time"

	"pay-mall/services/order-service/internal/mocks"
	"pay-mall/services/order-service/internal/model"

	"go.uber.org/mock/gomock"
)

func TestOrderService_CreateOrder_WithMockgen(t *testing.T) {
	tests := []struct {
		name        string
		req         *model.CreateOrderRequest
		setupMocks  func(*mocks.MockOrderRepository, *mocks.MockEventPublisher)
		wantErr     bool
		errContains string
	}{
		{
			name: "successful order creation",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items: []model.CreateOrderItem{
					{
						ProductID: "product-1",
						Quantity:  2,
						Price:     99.99,
					},
					{
						ProductID: "product-2",
						Quantity:  1,
						Price:     49.99,
					},
				},
			},
			setupMocks: func(mockRepo *mocks.MockOrderRepository, mockPublisher *mocks.MockEventPublisher) {
				// 期望调用Create方法一次，返回nil（成功）
				mockRepo.EXPECT().
					Create(gomock.Any()).
					Return(nil).
					Times(1)

				// 期望为每个订单项调用CreateOrderItem
				mockRepo.EXPECT().
					CreateOrderItem(gomock.Any()).
					Return(nil).
					Times(2) // 两个订单项

				// 期望发布订单创建事件
				mockPublisher.EXPECT().
					Publish(gomock.Any()).
					Return(nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "empty order items should fail",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items:  []model.CreateOrderItem{},
			},
			setupMocks: func(mockRepo *mocks.MockOrderRepository, mockPublisher *mocks.MockEventPublisher) {
				// 空订单项的情况下，不应该调用任何repository方法
				// 不设置任何EXPECT，如果被调用会失败
			},
			wantErr:     true,
			errContains: "order items cannot be empty",
		},
		{
			name: "repository create order error",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items: []model.CreateOrderItem{
					{
						ProductID: "product-1",
						Quantity:  1,
						Price:     99.99,
					},
				},
			},
			setupMocks: func(mockRepo *mocks.MockOrderRepository, mockPublisher *mocks.MockEventPublisher) {
				// Mock创建订单失败
				mockRepo.EXPECT().
					Create(gomock.Any()).
					Return(errors.New("database connection error")).
					Times(1)

				// 不应该调用CreateOrderItem和Publish
			},
			wantErr:     true,
			errContains: "database connection error",
		},
		{
			name: "repository create order item error",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items: []model.CreateOrderItem{
					{
						ProductID: "product-1",
						Quantity:  1,
						Price:     99.99,
					},
				},
			},
			setupMocks: func(mockRepo *mocks.MockOrderRepository, mockPublisher *mocks.MockEventPublisher) {
				// 创建订单成功
				mockRepo.EXPECT().
					Create(gomock.Any()).
					Return(nil).
					Times(1)

				// 创建订单项失败
				mockRepo.EXPECT().
					CreateOrderItem(gomock.Any()).
					Return(errors.New("failed to create order item")).
					Times(1)

				// 不应该调用Publish
			},
			wantErr:     true,
			errContains: "failed to create order item",
		},
		{
			name: "event publish error should not fail order creation",
			req: &model.CreateOrderRequest{
				UserID: "user-123",
				Items: []model.CreateOrderItem{
					{
						ProductID: "product-1",
						Quantity:  1,
						Price:     99.99,
					},
				},
			},
			setupMocks: func(mockRepo *mocks.MockOrderRepository, mockPublisher *mocks.MockEventPublisher) {
				// 创建订单和订单项都成功
				mockRepo.EXPECT().
					Create(gomock.Any()).
					Return(nil).
					Times(1)

				mockRepo.EXPECT().
					CreateOrderItem(gomock.Any()).
					Return(nil).
					Times(1)

				// 事件发布失败，但不应该影响订单创建
				mockPublisher.EXPECT().
					Publish(gomock.Any()).
					Return(errors.New("event publish failed")).
					Times(1)
			},
			wantErr: false, // 事件发布失败不应该导致订单创建失败
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建gomock控制器
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// 创建Mock对象
			mockRepo := mocks.NewMockOrderRepository(ctrl)
			mockPublisher := mocks.NewMockEventPublisher(ctrl)

			// 设置Mock期望
			if tt.setupMocks != nil {
				tt.setupMocks(mockRepo, mockPublisher)
			}

			// 创建服务实例
			service := NewOrderService(mockRepo, mockPublisher)

			// 执行测试
			result, err := service.CreateOrder(tt.req)

			// 验证结果
			if tt.wantErr {
				if err == nil {
					t.Errorf("CreateOrder() expected error, got nil")
				}
				if tt.errContains != "" && err != nil {
					if !contains(err.Error(), tt.errContains) {
						t.Errorf("CreateOrder() error = %v, want error containing %v", err, tt.errContains)
					}
				}
			} else {
				if err != nil {
					t.Errorf("CreateOrder() unexpected error = %v", err)
				}
				if result == nil {
					t.Error("CreateOrder() expected result, got nil")
				}
			}
		})
	}
}

func TestOrderService_GetOrderByID_WithMockgen(t *testing.T) {
	tests := []struct {
		name        string
		orderID     string
		setupMocks  func(*mocks.MockOrderRepository, *mocks.MockEventPublisher)
		wantErr     bool
		errContains string
	}{
		{
			name:    "successful order retrieval",
			orderID: "order-123",
			setupMocks: func(mockRepo *mocks.MockOrderRepository, mockPublisher *mocks.MockEventPublisher) {
				// Mock找到订单
				order := &model.Order{
					ID:          "order-123",
					UserID:      "user-123",
					TotalAmount: 199.98,
					Status:      model.OrderStatusPending,
				}
				order.SetCreatedAt(time.Now())
				order.SetUpdatedAt(time.Now())

				mockRepo.EXPECT().
					FindByID("order-123").
					Return(order, nil).
					Times(1)

				// Mock找到订单项
				items := []*model.OrderItem{
					{
						ID:        "item-1",
						OrderID:   "order-123",
						ProductID: "product-1",
						Quantity:  2,
						Price:     99.99,
					},
				}

				mockRepo.EXPECT().
					FindItemsByOrderID("order-123").
					Return(items, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name:    "order not found",
			orderID: "non-existing-order",
			setupMocks: func(mockRepo *mocks.MockOrderRepository, mockPublisher *mocks.MockEventPublisher) {
				// Mock未找到订单
				mockRepo.EXPECT().
					FindByID("non-existing-order").
					Return(nil, nil).
					Times(1)
			},
			wantErr:     true,
			errContains: "order not found",
		},
		{
			name:    "repository error",
			orderID: "error-order",
			setupMocks: func(mockRepo *mocks.MockOrderRepository, mockPublisher *mocks.MockEventPublisher) {
				// Mock数据库错误
				mockRepo.EXPECT().
					FindByID("error-order").
					Return(nil, errors.New("database connection failed")).
					Times(1)
			},
			wantErr:     true,
			errContains: "database connection failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建gomock控制器
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// 创建Mock对象
			mockRepo := mocks.NewMockOrderRepository(ctrl)
			mockPublisher := mocks.NewMockEventPublisher(ctrl)

			// 设置Mock期望
			if tt.setupMocks != nil {
				tt.setupMocks(mockRepo, mockPublisher)
			}

			// 创建服务实例
			service := NewOrderService(mockRepo, mockPublisher)

			// 执行测试
			result, err := service.GetOrderByID(tt.orderID)

			// 验证结果
			if tt.wantErr {
				if err == nil {
					t.Errorf("GetOrderByID() expected error, got nil")
				}
				if tt.errContains != "" && err != nil {
					if !contains(err.Error(), tt.errContains) {
						t.Errorf("GetOrderByID() error = %v, want error containing %v", err, tt.errContains)
					}
				}
			} else {
				if err != nil {
					t.Errorf("GetOrderByID() unexpected error = %v", err)
				}
				if result == nil {
					t.Error("GetOrderByID() expected result, got nil")
				}
			}
		})
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && s[:len(substr)] == substr) ||
		(len(s) > len(substr) && s[len(s)-len(substr):] == substr) ||
		containsHelper(s, substr))
}

func containsHelper(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
