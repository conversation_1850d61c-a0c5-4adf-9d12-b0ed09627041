package model

import (
	"database/sql"
	"time"
)

// Order 订单模型
type Order struct {
	ID          string       `json:"id" db:"id"`
	UserID      string       `json:"user_id" db:"user_id"`
	TotalAmount float64      `json:"total_amount" db:"total_amount"`
	Status      string       `json:"status" db:"status"`
	CreatedAt   sql.NullTime `json:"created_at" db:"created_at"`
	UpdatedAt   sql.NullTime `json:"updated_at" db:"updated_at"`
}

// OrderItem 订单项模型
type OrderItem struct {
	ID        string  `json:"id" db:"id"`
	OrderID   string  `json:"order_id" db:"order_id"`
	ProductID string  `json:"product_id" db:"product_id"`
	Quantity  int     `json:"quantity" db:"quantity"`
	Price     float64 `json:"price" db:"price"`
}

// OrderStatus 订单状态常量
const (
	OrderStatusPending   = "PENDING"   // 待处理
	OrderStatusReserved  = "RESERVED"  // 已预留库存
	OrderStatusPaid      = "PAID"      // 已支付
	OrderStatusShipped   = "SHIPPED"   // 已发货
	OrderStatusDelivered = "DELIVERED" // 已送达
	OrderStatusCancelled = "CANCELLED" // 已取消
	OrderStatusRefunded  = "REFUNDED"  // 已退款
)

// SetCreatedAt 设置创建时间
func (o *Order) SetCreatedAt(t time.Time) {
	o.CreatedAt = sql.NullTime{Time: t, Valid: true}
}

// SetUpdatedAt 设置更新时间
func (o *Order) SetUpdatedAt(t time.Time) {
	o.UpdatedAt = sql.NullTime{Time: t, Valid: true}
}

// ToOrderResponse 转换为订单响应
func (o *Order) ToOrderResponse() OrderResponse {
	return OrderResponse{
		ID:          o.ID,
		UserID:      o.UserID,
		TotalAmount: o.TotalAmount,
		Status:      o.Status,
		CreatedAt:   o.CreatedAt.Time,
		UpdatedAt:   o.UpdatedAt.Time,
	}
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	UserID string             `json:"user_id" binding:"required"`
	Items  []CreateOrderItem  `json:"items" binding:"required,min=1"`
}

// CreateOrderItem 创建订单项
type CreateOrderItem struct {
	ProductID string  `json:"product_id" binding:"required"`
	Quantity  int     `json:"quantity" binding:"required,min=1"`
	Price     float64 `json:"price" binding:"required,min=0"`
}

// OrderResponse 订单响应
type OrderResponse struct {
	ID          string              `json:"id"`
	UserID      string              `json:"user_id"`
	TotalAmount float64             `json:"total_amount"`
	Status      string              `json:"status"`
	Items       []OrderItemResponse `json:"items,omitempty"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
}

// OrderItemResponse 订单项响应
type OrderItemResponse struct {
	ID        string  `json:"id"`
	ProductID string  `json:"product_id"`
	Quantity  int     `json:"quantity"`
	Price     float64 `json:"price"`
}

// OrderListResponse 订单列表响应
type OrderListResponse struct {
	Orders   []OrderResponse `json:"orders"`
	Page     int             `json:"page"`
	PageSize int             `json:"page_size"`
	Total    int64           `json:"total"`
}

// UpdateOrderStatusRequest 更新订单状态请求
type UpdateOrderStatusRequest struct {
	Status string `json:"status" binding:"required"`
}
