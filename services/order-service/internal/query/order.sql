-- name: <PERSON>reateOrder :exec
INSERT INTO orders (id, user_id, total_amount, status, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?);

-- name: CreateOrderItem :exec
INSERT INTO order_items (id, order_id, product_id, quantity, price)
VALUES (?, ?, ?, ?, ?);

-- name: GetOrderByID :one
SELECT * FROM orders WHERE id = ?;

-- name: GetOrdersByUserID :many
SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?;

-- name: GetOrderItemsByOrderID :many
SELECT * FROM order_items WHERE order_id = ?;

-- name: UpdateOrderStatus :exec
UPDATE orders SET status = ?, updated_at = ? WHERE id = ?;

-- name: DeleteOrder :exec
DELETE FROM orders WHERE id = ?;

-- name: DeleteOrderItems :exec
DELETE FROM order_items WHERE order_id = ?;

-- name: ListOrders :many
SELECT * FROM orders ORDER BY created_at DESC LIMIT ? OFFSET ?;

-- name: CountOrders :one
SELECT COUNT(*) FROM orders;

-- name: CountOrdersByUserID :one
SELECT COUNT(*) FROM orders WHERE user_id = ?;
