// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: order.sql

package query

import (
	"context"
	"database/sql"
)

const countOrders = `-- name: CountOrders :one
SELECT COUNT(*) FROM orders
`

func (q *Queries) CountOrders(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, countOrders)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countOrdersByUserID = `-- name: CountOrdersByUserID :one
SELECT COUNT(*) FROM orders WHERE user_id = ?
`

func (q *Queries) CountOrdersByUserID(ctx context.Context, userID string) (int64, error) {
	row := q.db.QueryRowContext(ctx, countOrdersByUserID, userID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createOrder = `-- name: CreateOrder :exec
INSERT INTO orders (id, user_id, total_amount, status, created_at, updated_at)
VALUES (?, ?, ?, ?, ?, ?)
`

type CreateOrderParams struct {
	ID          string
	UserID      string
	TotalAmount string
	Status      string
	CreatedAt   sql.NullTime
	UpdatedAt   sql.NullTime
}

func (q *Queries) CreateOrder(ctx context.Context, arg CreateOrderParams) error {
	_, err := q.db.ExecContext(ctx, createOrder,
		arg.ID,
		arg.UserID,
		arg.TotalAmount,
		arg.Status,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	return err
}

const createOrderItem = `-- name: CreateOrderItem :exec
INSERT INTO order_items (id, order_id, product_id, quantity, price)
VALUES (?, ?, ?, ?, ?)
`

type CreateOrderItemParams struct {
	ID        string
	OrderID   string
	ProductID string
	Quantity  int32
	Price     string
}

func (q *Queries) CreateOrderItem(ctx context.Context, arg CreateOrderItemParams) error {
	_, err := q.db.ExecContext(ctx, createOrderItem,
		arg.ID,
		arg.OrderID,
		arg.ProductID,
		arg.Quantity,
		arg.Price,
	)
	return err
}

const deleteOrder = `-- name: DeleteOrder :exec
DELETE FROM orders WHERE id = ?
`

func (q *Queries) DeleteOrder(ctx context.Context, id string) error {
	_, err := q.db.ExecContext(ctx, deleteOrder, id)
	return err
}

const deleteOrderItems = `-- name: DeleteOrderItems :exec
DELETE FROM order_items WHERE order_id = ?
`

func (q *Queries) DeleteOrderItems(ctx context.Context, orderID string) error {
	_, err := q.db.ExecContext(ctx, deleteOrderItems, orderID)
	return err
}

const getOrderByID = `-- name: GetOrderByID :one
SELECT id, user_id, total_amount, status, created_at, updated_at FROM orders WHERE id = ?
`

func (q *Queries) GetOrderByID(ctx context.Context, id string) (Order, error) {
	row := q.db.QueryRowContext(ctx, getOrderByID, id)
	var i Order
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.TotalAmount,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getOrderItemsByOrderID = `-- name: GetOrderItemsByOrderID :many
SELECT id, order_id, product_id, quantity, price FROM order_items WHERE order_id = ?
`

func (q *Queries) GetOrderItemsByOrderID(ctx context.Context, orderID string) ([]OrderItem, error) {
	rows, err := q.db.QueryContext(ctx, getOrderItemsByOrderID, orderID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []OrderItem
	for rows.Next() {
		var i OrderItem
		if err := rows.Scan(
			&i.ID,
			&i.OrderID,
			&i.ProductID,
			&i.Quantity,
			&i.Price,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getOrdersByUserID = `-- name: GetOrdersByUserID :many
SELECT id, user_id, total_amount, status, created_at, updated_at FROM orders WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?
`

type GetOrdersByUserIDParams struct {
	UserID string
	Limit  int32
	Offset int32
}

func (q *Queries) GetOrdersByUserID(ctx context.Context, arg GetOrdersByUserIDParams) ([]Order, error) {
	rows, err := q.db.QueryContext(ctx, getOrdersByUserID, arg.UserID, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Order
	for rows.Next() {
		var i Order
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.TotalAmount,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOrders = `-- name: ListOrders :many
SELECT id, user_id, total_amount, status, created_at, updated_at FROM orders ORDER BY created_at DESC LIMIT ? OFFSET ?
`

type ListOrdersParams struct {
	Limit  int32
	Offset int32
}

func (q *Queries) ListOrders(ctx context.Context, arg ListOrdersParams) ([]Order, error) {
	rows, err := q.db.QueryContext(ctx, listOrders, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Order
	for rows.Next() {
		var i Order
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.TotalAmount,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateOrderStatus = `-- name: UpdateOrderStatus :exec
UPDATE orders SET status = ?, updated_at = ? WHERE id = ?
`

type UpdateOrderStatusParams struct {
	Status    string
	UpdatedAt sql.NullTime
	ID        string
}

func (q *Queries) UpdateOrderStatus(ctx context.Context, arg UpdateOrderStatusParams) error {
	_, err := q.db.ExecContext(ctx, updateOrderStatus, arg.Status, arg.UpdatedAt, arg.ID)
	return err
}
