server:
  port: "8083"
  host: "0.0.0.0"
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "123456"
  database: "order_service_db"
  ssl_mode: "disable"
redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0
log:
  level: "debug"
  format: "json"
  file: "stdout"
jwt:
  secret: "your-secret-key-change-in-production"
  expire_hour: 24
kafka:
  brokers:
    - "localhost:9092"
  topic: "pay-mall-events"
  enabled: true
