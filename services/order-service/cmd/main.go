package main

import (
	"log"
	"net/http"

	"pay-mall/services/order-service/internal/config"
	"pay-mall/services/order-service/internal/database"
	"pay-mall/services/order-service/internal/event"
	"pay-mall/services/order-service/internal/handler"
	"pay-mall/services/order-service/internal/repository"
	"pay-mall/services/order-service/internal/service"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 连接数据库
	db, err := database.NewConnection(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 初始化依赖
	eventPublisher := event.NewMockEventPublisher()
	orderRepo := repository.NewOrderRepository(db)
	orderService := service.NewOrderService(orderRepo, eventPublisher)
	orderHandler := handler.NewOrderHandler(orderService)

	// 设置路由
	router := gin.Default()

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "order-service",
		})
	})

	// 订单相关路由
	v1 := router.Group("/api/v1")
	{
		orders := v1.Group("/orders")
		{
			orders.POST("", orderHandler.CreateOrder)                 // 创建订单
			orders.GET("", orderHandler.ListOrders)                   // 获取订单列表
			orders.GET("/:id", orderHandler.GetOrder)                 // 获取订单详情
			orders.PUT("/:id/status", orderHandler.UpdateOrderStatus) // 更新订单状态
			orders.DELETE("/:id", orderHandler.CancelOrder)           // 取消订单
		}

		// 用户订单路由
		users := v1.Group("/users")
		{
			users.GET("/:user_id/orders", orderHandler.GetUserOrders) // 获取用户订单列表
		}
	}

	// 启动服务器
	addr := cfg.Server.Host + ":" + cfg.Server.Port
	log.Printf("Order service starting on %s", addr)
	if err := router.Run(addr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
