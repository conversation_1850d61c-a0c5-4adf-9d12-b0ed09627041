package main

import (
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func main() {
	r := gin.Default()

	r.GET("/ping", func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"message": "pong",
		})
	})

	port := "8082" // Inventory Service 端口
	fmt.Printf("Inventory Service running on :%s\n", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatalf("Inventory Service failed to start: %v", err)
	}
}