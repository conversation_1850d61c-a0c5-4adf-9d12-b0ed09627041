package main

import (
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func main() {
	r := gin.Default()

	r.GET("/ping", func(c *gin.Context) {
		c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
			"message": "pong",
		})
	})

	port := "8084" // Payment Service 端口
	fmt.Printf("Payment Service running on :%s\n", port)
	if err := r.Run(":" + port); err != nil {
		log.Fatalf("Payment Service failed to start: %v", err)
	}
}