# user-service 服务配置管理方案规划

## 规划目标

将 `user-service/internal/config/config.go` 中的配置从环境变量加载改为从 YAML 文件加载，并支持区分本地 (local) 和生产 (production) 环境的配置。

## 详细规划

### 1. 配置结构体修改 (`user-service/internal/config/config.go`)

为了支持 YAML 文件的解析，`Config` 及其嵌套结构体中的字段标签需要从 `json:"..."` 修改为 `yaml:"..."`。同时，根据 `main.go` 中对日志配置的使用，需要为 `Config` 结构体添加 `LogConfig` 字段。

```go
package config

import (
	"fmt"
	"os"
	// 移除 strconv，因为不再从环境变量解析整数
	// 移除 os，因为不再直接使用 os.Getenv
)

// Config 应用配置
type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	Redis    RedisConfig    `yaml:"redis"`
	Log      LogConfig      `yaml:"log"` // 新增日志配置
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `yaml:"port"`
	Host string `yaml:"host"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Database string `yaml:"database"`
	SSLMode  string `yaml:"ssl_mode"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Password string `yaml:"password"`
	Database int    `yaml:"database"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	File   string `yaml:"file"`
}

// LoadConfig 加载配置 (此函数将在后续步骤中详细规划)
func LoadConfig(configPath string) (*Config, error) {
	// ... 实现细节 ...
	return nil, nil
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		c.Username, c.Password, c.Host, c.Port, c.Database)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// 移除 getEnv 和 getEnvAsInt 函数，因为不再从环境变量加载
```

### 2. YAML 文件结构

建议在 `user-service` 项目根目录下创建 `configs` 文件夹，用于存放不同环境的 YAML 配置文件。

**文件结构示意图：**

```mermaid
graph TD
    A[user-service/] --> B[cmd/]
    A --> C[internal/]
    A --> D[configs/]
    B --> B1[main.go]
    C --> C1[config/]
    C1 --> C11[config.go]
    D --> D1[local.yaml]
    D --> D2[production.yaml]
```

**`user-service/configs/local.yaml` 示例：**

```yaml
server:
  port: "8081"
  host: "0.0.0.0"
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: ""
  database: "user_service_local"
  ssl_mode: "disable"
redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0
log:
  level: "debug"
  format: "json"
  file: "stdout"
```

**`user-service/configs/production.yaml` 示例：**

```yaml
server:
  port: "80"
  host: "0.0.0.0"
database:
  host: "db.production.example.com"
  port: 5432 # 示例：生产环境可能使用不同的数据库类型或端口
  username: "prod_user"
  password: "prod_password"
  database: "user_service_prod"
  ssl_mode: "require"
redis:
  host: "redis.production.example.com"
  port: 6379
  password: "prod_redis_password"
  database: 0
log:
  level: "info"
  format: "json"
  file: "/var/log/user-service/app.log"
```

### 3. 配置加载逻辑 (`user-service/internal/config/config.go`)

修改 `LoadConfig` 函数，使其能够根据环境变量 `APP_ENV` 加载对应的 YAML 文件。如果 `APP_ENV` 未设置，则默认加载 `local.yaml`。

**配置加载流程图：**

```mermaid
graph TD
    A[启动应用] --> B{读取 APP_ENV 环境变量?};
    B -- 是 --> C[获取环境名 (e.g., local, production)];
    B -- 否 --> D[默认环境为 local];
    C --> E[拼接配置文件路径: configs/{env}.yaml];
    D --> E;
    E --> F[读取 YAML 文件内容];
    F -- 成功 --> G[使用 yaml.Unmarshal 解析到 Config 结构体];
    F -- 失败 --> H[返回错误];
    G -- 成功 --> I[返回 Config 对象];
    G -- 失败 --> H;
    I --> J[应用使用 Config 对象];
```

**`LoadConfig` 函数伪代码：**

```go
// LoadConfig 加载配置
// configPath 参数可选，如果为空则根据 APP_ENV 环境变量加载
func LoadConfig(configPath string) (*Config, error) {
	var cfg Config

	// 确定配置文件路径
	if configPath == "" {
		env := os.Getenv("APP_ENV")
		if env == "" {
			env = "local" // 默认环境为 local
		}
		// 假设配置文件在项目根目录下的 configs 文件夹
		// 注意：这里需要根据实际运行时的相对路径调整，或者使用绝对路径
		configPath = fmt.Sprintf("configs/%s.yaml", env)
	}

	// 读取文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", configPath, err)
	}

	// 解析 YAML
	// 这里将使用推荐的 YAML 库：gopkg.in/yaml.v3
	err = yaml.Unmarshal(data, &cfg) // 需要导入 "gopkg.in/yaml.v3"
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config file %s: %w", configPath, err)
	}

	return &cfg, nil
}
```

### 4. 依赖库选择

**推荐库：** [`gopkg.in/yaml.v3`](https://pkg.go.dev/gopkg.in/yaml.v3)

**理由：**

*   **官方推荐与社区标准：** 它是 Go 语言社区广泛接受和使用的 YAML 库，被认为是事实上的标准。
*   **功能完善：** 支持 YAML 1.2 规范，包括锚点、别名、标签等高级特性，能够满足复杂的配置需求。
*   **性能良好：** 经过优化，在处理大型配置文件时表现良好，不会成为性能瓶颈。
*   **易于使用：** API 设计与 Go 标准库的 `encoding/json` 类似，对于 Go 开发者来说学习成本低，易于上手。
*   **活跃维护：** 社区活跃，持续更新和维护，能够及时获得 bug 修复和新功能支持。
*   **结构体标签支持：** 完美支持 `yaml:"field_name"` 标签，可以直接将 YAML 内容映射到 Go 结构体，简化了配置解析的代码。

### 5. `main.go` 调整点 (`user-service/cmd/main.go`)

`main.go` 中的调整相对较少，主要集中在配置加载的调用上。

1.  **导入路径：** 确保 `pay-mall/services/user-service/internal/config` 导入路径正确。
2.  **`config.LoadConfig` 调用：** 保持 `cfg, err := config.LoadConfig("")` 不变，因为 `LoadConfig` 函数将根据 `APP_ENV` 环境变量或默认值自动选择配置文件。
3.  **错误处理：** 确保 `LoadConfig` 返回错误时，`main.go` 中的错误处理逻辑能够正确捕获并退出。

**`main.go` 伪代码调整：**

```go
package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"pay-mall/services/user-service/internal/config" // 确保导入路径正确
	"github.com/formal-you/pkg/logger"
	"go.uber.org/zap"
	"pay-mall/services/user-service/internal/database"
	"pay-mall/services/user-service/internal/event"
	"pay-mall/services/user-service/internal/handler"
	"pay-mall/services/user-service/internal/repository"
	"pay-mall/services/user-service/internal/service"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	// LoadConfig 函数现在会根据 APP_ENV 环境变量或默认值加载相应的 YAML 文件
	cfg, err := config.LoadConfig("") // 传入空字符串表示让 LoadConfig 自动处理环境配置
	if err != nil {
		log.Fatalf("Failed to load config: %v", err) // 错误处理保持不变
	}

	// 初始化日志
	// 确保 cfg.Log 字段可用，因为已在 Config 结构体中添加
	logger := logger.NewLogger(cfg.Log.Level, cfg.Log.Format, cfg.Log.File)
	defer logger.Sync()

	// ... 后续代码保持不变，因为 cfg 结构体内容不变，只是加载方式变了
	// 初始化数据库
	db, err := database.NewDatabase(cfg)
	if err != nil {
		logger.Fatal("Failed to connect to database", zap.Error(err))
	}
	defer db.Close()

	// ... (其他初始化和启动逻辑)
}