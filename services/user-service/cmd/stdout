{"level":"fatal","ts":"2025-06-30T13:06:13.360+0800","caller":"runtime/proc.go:283","msg":"Failed to connect to database","error":"failed to ping database: Error 1045 (28000): Access denied for user 'root'@'**********' (using password: NO)"}
{"level":"fatal","ts":"2025-06-30T13:06:47.115+0800","caller":"runtime/proc.go:283","msg":"Failed to connect to database","error":"failed to ping database: Error 1045 (28000): Access denied for user 'root'@'**********' (using password: NO)"}
{"level":"fatal","ts":"2025-06-30T13:08:10.993+0800","caller":"runtime/proc.go:283","msg":"Failed to connect to database","error":"failed to ping database: <PERSON>rror 1045 (28000): Access denied for user 'root'@'**********' (using password: NO)"}
{"level":"info","ts":"2025-06-30T13:36:21.294+0800","caller":"runtime/asm_amd64.s:1700","msg":"User Service starting","host":"0.0.0.0","port":"8081"}
{"level":"info","ts":"2025-06-30T13:46:23.764+0800","caller":"runtime/proc.go:283","msg":"Shutting down server..."}
{"level":"info","ts":"2025-06-30T13:46:23.765+0800","caller":"runtime/proc.go:283","msg":"Server exited"}
{"level":"info","ts":"2025-06-30T14:04:01.217+0800","caller":"runtime/asm_amd64.s:1700","msg":"User Service starting","host":"0.0.0.0","port":"8081"}
{"level":"info","ts":"2025-06-30T14:13:45.951+0800","caller":"runtime/proc.go:283","msg":"Shutting down server..."}
{"level":"info","ts":"2025-06-30T14:13:45.951+0800","caller":"runtime/proc.go:283","msg":"Server exited"}
