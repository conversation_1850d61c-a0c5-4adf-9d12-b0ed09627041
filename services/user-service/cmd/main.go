package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"pay-mall/services/user-service/internal/config"
	"pay-mall/services/user-service/internal/database"
	"pay-mall/services/user-service/internal/event"
	"pay-mall/services/user-service/internal/handler"
	"pay-mall/services/user-service/internal/repository"
	"pay-mall/services/user-service/internal/service"

	"pay-mall/pkg/logger"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("./configs/local.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger := logger.NewLogger(cfg.Log.Level, cfg.Log.Format, cfg.Log.File)
	defer logger.Sync()

	// 初始化数据库
	db, err := database.NewDatabase(cfg)
	if err != nil {
		logger.Fatal("Failed to connect to database", zap.String("dsn", cfg.Database.GetDSN()), zap.Error(err))
	}
	defer db.Close()

	// 初始化事件发布器
	eventPublisher := event.NewMockEventPublisher()

	// 初始化仓库
	userRepo := repository.NewUserRepository(db)

	// 初始化服务
	userService := service.NewUserService(userRepo, eventPublisher, service.NewAppConfigAdapter(cfg))

	// 初始化处理器
	userHandler := handler.NewUserHandler(userService)

	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建路由
	r := gin.Default()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"service":   "user-service",
			"timestamp": time.Now().Unix(),
		})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 用户相关路由
		users := api.Group("/users")
		{
			users.POST("/register", userHandler.RegisterUser)
			users.POST("/login", userHandler.LoginUser)
			users.GET("", userHandler.ListUsers)
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)
		}
	}

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port),
		Handler: r,
	}

	// 启动服务器
	go func() {
		logger.Info("User Service starting",
			zap.String("host", cfg.Server.Host),
			zap.String("port", cfg.Server.Port))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err = srv.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", zap.Error(err))
	}

	logger.Info("Server exited")
}
