package database

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"pay-mall/services/user-service/internal/config" // 修改导入路径

	_ "github.com/go-sql-driver/mysql"
)

// NewDatabase 创建数据库连接
func NewDatabase(cfg *config.Config) (*sql.DB, error) { // 修改参数类型
	dsn := cfg.Database.GetDSN() // 使用 GetDSN 方法获取 DSN

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)
	db.SetConnMaxLifetime(5 * time.Minute)

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connected successfully")

	return db, nil
}
