package handler_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"pay-mall/services/user-service/internal/handler"
	"pay-mall/services/user-service/internal/model"

	"github.com/gin-gonic/gin"
)

// MockUserService 是 service.UserService 接口的模拟实现
// 手动实现，不依赖 testify/mock
type MockUserService struct {
	T *testing.T // 添加一个 *testing.T 字段

	RegisterUserFunc func(req *model.UserRegistrationRequest) (*model.UserResponse, error)
	LoginUserFunc    func(req *model.UserLoginRequest) (*model.LoginResponse, error)
	GetUserByIDFunc  func(id string) (*model.UserResponse, error)
	UpdateUserFunc   func(id string, req *model.UserRegistrationRequest) (*model.UserResponse, error)
	DeleteUserFunc   func(id string) error
}

func (m *MockUserService) RegisterUser(req *model.UserRegistrationRequest) (*model.UserResponse, error) {
	if m.RegisterUserFunc != nil {
		return m.RegisterUserFunc(req)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("RegisterUser not mocked, but was called")
	}
	return nil, errors.New("RegisterUser not mocked")
}

func (m *MockUserService) LoginUser(req *model.UserLoginRequest) (*model.LoginResponse, error) {
	if m.LoginUserFunc != nil {
		return m.LoginUserFunc(req)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("LoginUser not mocked, but was called")
	}
	return nil, errors.New("LoginUser not mocked")
}

func (m *MockUserService) GetUserByID(id string) (*model.UserResponse, error) {
	if m.GetUserByIDFunc != nil {
		return m.GetUserByIDFunc(id)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("GetUserByID not mocked, but was called")
	}
	return nil, errors.New("GetUserByID not mocked")
}

func (m *MockUserService) UpdateUser(id string, req *model.UserRegistrationRequest) (*model.UserResponse, error) {
	if m.UpdateUserFunc != nil {
		return m.UpdateUserFunc(id, req)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("UpdateUser not mocked, but was called")
	}
	return nil, errors.New("UpdateUser not mocked")
}

func (m *MockUserService) DeleteUser(id string) error {
	if m.DeleteUserFunc != nil {
		return m.DeleteUserFunc(id)
	}
	if m.T != nil {
		m.T.Helper()
		m.T.Fatal("DeleteUser not mocked, but was called")
	}
	return errors.New("DeleteUser not mocked")
}

// setupRouter 设置 Gin 路由器并注入模拟依赖
func setupRouter(mockUserService *MockUserService) *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.Default()
	r.RedirectTrailingSlash = false // 禁用自动重定向

	userHandler := handler.NewUserHandler(mockUserService)

	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"service":   "user-service",
			"timestamp": time.Now().Unix(),
		})
	})

	api := r.Group("/api/v1")
	{
		users := api.Group("/users")
		{
			users.POST("/register", userHandler.RegisterUser)
			users.POST("/login", userHandler.LoginUser)
			users.GET("", userHandler.ListUsers)
			users.GET("/", userHandler.GetUser) // 处理 /api/v1/users/ 的情况
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)
		}
	}
	return r
}

func TestHealthCheck(t *testing.T) {
	mockUserService := &MockUserService{} // 使用手动模拟
	router := setupRouter(mockUserService)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/health", nil)
	router.ServeHTTP(w, req)

	// 使用标准库的 testing.T.Errorf 进行断言
	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}
	if status, ok := response["status"].(string); !ok || status != "ok" {
		t.Errorf("Expected status 'ok', got %v", response["status"])
	}
	if service, ok := response["service"].(string); !ok || service != "user-service" {
		t.Errorf("Expected service 'user-service', got %v", service)
	}
	if _, ok := response["timestamp"].(float64); !ok { // JSON unmarshals numbers to float64
		t.Errorf("Expected timestamp, got %v", response["timestamp"])
	}
}

func TestRegisterUser(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    model.UserRegistrationRequest
		mockFunc       func(req *model.UserRegistrationRequest) (*model.UserResponse, error)
		expectedStatus int
		expectedBody   map[string]interface{} // 修改为 map[string]interface{}
	}{
		{
			name: "成功注册",
			requestBody: model.UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			mockFunc: func(req *model.UserRegistrationRequest) (*model.UserResponse, error) {
				return &model.UserResponse{
					ID:        "user-123",
					Username:  req.Username,
					Email:     req.Email,
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil
			},
			expectedStatus: http.StatusCreated,
			expectedBody: map[string]interface{}{
				"message": "User registered successfully",
				"user": map[string]interface{}{
					"id":       "user-123",
					"username": "testuser",
					"email":    "<EMAIL>",
				},
			},
		},
		{
			name: "用户名已存在",
			requestBody: model.UserRegistrationRequest{
				Username: "existinguser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			mockFunc: func(req *model.UserRegistrationRequest) (*model.UserResponse, error) {
				return nil, errors.New("username already exists")
			},
			expectedStatus: http.StatusConflict,
			expectedBody: map[string]interface{}{
				"error": "username already exists",
			},
		},
		{
			name: "邮箱已存在",
			requestBody: model.UserRegistrationRequest{
				Username: "newuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			mockFunc: func(req *model.UserRegistrationRequest) (*model.UserResponse, error) {
				return nil, errors.New("email already exists")
			},
			expectedStatus: http.StatusConflict,
			expectedBody: map[string]interface{}{
				"error": "email already exists",
			},
		},
		{
			name: "无效请求数据",
			requestBody: model.UserRegistrationRequest{
				Username: "", // 缺失用户名
				Email:    "invalid-email",
				Password: "123", // 密码过短
			},
			mockFunc:       nil, // ShouldBindJSON 会处理此错误，不调用 mock
			expectedStatus: http.StatusBadRequest,
			expectedBody: map[string]interface{}{
				"error": "Invalid request data",
			},
		},
		{
			name: "内部服务器错误",
			requestBody: model.UserRegistrationRequest{
				Username: "erroruser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			mockFunc: func(req *model.UserRegistrationRequest) (*model.UserResponse, error) {
				return nil, errors.New("failed to register user")
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"error": "Failed to register user",
			},
		},
	}

	for _, tt := range tests {
		testCase := tt // 捕获当前 tt 的值
		t.Run(testCase.name, func(t *testing.T) {
			mockUserService := &MockUserService{
				T: t,
			}

			// 根据测试用例设置 mockFunc
			if testCase.mockFunc != nil {
				mockUserService.RegisterUserFunc = func(req *model.UserRegistrationRequest) (*model.UserResponse, error) {
					if req.Username != testCase.requestBody.Username || req.Email != testCase.requestBody.Email || req.Password != testCase.requestBody.Password {
						t.Errorf("RegisterUser called with unexpected request: got %+v, want %+v", req, testCase.requestBody)
					}
					return testCase.mockFunc(req)
				}
			}

			router := setupRouter(mockUserService)

			jsonBody, _ := json.Marshal(testCase.requestBody)
			w := httptest.NewRecorder()
			req, _ := http.NewRequest("POST", "/api/v1/users/register", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")
			router.ServeHTTP(w, req)

			if w.Code != testCase.expectedStatus {
				t.Errorf("Expected status %d, got %d. Body: %s", testCase.expectedStatus, w.Code, w.Body.String())
			}

			var actualBody map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &actualBody)
			if err != nil {
				t.Fatalf("Failed to unmarshal actual response body: %v", err)
			}

			// 比较 error 字段
			if expectedErr, ok := testCase.expectedBody["error"]; ok {
				if actualErr, ok := actualBody["error"]; !ok || actualErr != expectedErr {
					t.Errorf("Expected error %q, got %q", expectedErr, actualErr)
				}
			} else if expectedMsg, ok := testCase.expectedBody["message"]; ok {
				// 比较 message 字段
				if actualMsg, ok := actualBody["message"]; !ok || actualMsg != expectedMsg {
					t.Errorf("Expected message %q, got %q", expectedMsg, actualMsg)
				}
				// 比较 user 字段 (仅在成功注册时)
				if expectedUser, ok := testCase.expectedBody["user"].(map[string]interface{}); ok {
					if actualUser, ok := actualBody["user"].(map[string]interface{}); ok {
						if actualUser["id"] != expectedUser["id"] ||
							actualUser["username"] != expectedUser["username"] ||
							actualUser["email"] != expectedUser["email"] {
							t.Errorf("Expected user %v, got %v", expectedUser, actualUser)
						}
					} else {
						t.Errorf("Expected user object, got %v", actualBody["user"])
					}
				}
			}
		})
	}
}

func TestLoginUser(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    model.UserLoginRequest
		mockFunc       func(req *model.UserLoginRequest) (*model.LoginResponse, error)
		expectedStatus int
		expectedBody   map[string]interface{} // 修改为 map[string]interface{}
	}{
		{
			name: "成功登录",
			requestBody: model.UserLoginRequest{
				Username: "testuser",
				Password: "password123",
			},
			mockFunc: func(req *model.UserLoginRequest) (*model.LoginResponse, error) {
				return &model.LoginResponse{
					Token: "mock-jwt-token",
					User: model.UserResponse{
						ID:        "user-123",
						Username:  req.Username,
						Email:     "<EMAIL>",
						CreatedAt: time.Now(), // 设置时间
						UpdatedAt: time.Now(), // 设置时间
					},
					ExpiresAt: time.Now().Add(time.Hour),
				}, nil
			},
			expectedStatus: http.StatusOK,
			expectedBody: map[string]interface{}{
				"message": "Login successful",
				"data": map[string]interface{}{
					"token": "mock-jwt-token",
					"user": map[string]interface{}{
						"id":       "user-123",
						"username": "testuser",
						"email":    "<EMAIL>",
					},
				},
			},
		},
		{
			name: "无效用户名或密码",
			requestBody: model.UserLoginRequest{
				Username: "wronguser",
				Password: "wrongpassword",
			},
			mockFunc: func(req *model.UserLoginRequest) (*model.LoginResponse, error) {
				return nil, errors.New("invalid username or password")
			},
			expectedStatus: http.StatusUnauthorized,
			expectedBody: map[string]interface{}{
				"error": "Invalid username or password",
			},
		},
		{
			name: "无效请求数据",
			requestBody: model.UserLoginRequest{
				Username: "", // 缺失用户名
				Password: "", // 缺失密码
			},
			mockFunc:       nil, // ShouldBindJSON 会处理此错误，不调用 mock
			expectedStatus: http.StatusBadRequest,
			expectedBody: map[string]interface{}{
				"error": "Invalid request data",
			},
		},
		{
			name: "内部服务器错误",
			requestBody: model.UserLoginRequest{
				Username: "erroruser",
				Password: "password123",
			},
			mockFunc: func(req *model.UserLoginRequest) (*model.LoginResponse, error) {
				return nil, errors.New("failed to login")
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"error": "Failed to login",
			},
		},
	}

	for _, tt := range tests {
		testCase := tt // 捕获当前 tt 的值
		t.Run(testCase.name, func(t *testing.T) {
			mockUserService := &MockUserService{
				T: t,
			}

			// 根据测试用例设置 mockFunc
			if testCase.mockFunc != nil {
				mockUserService.LoginUserFunc = func(req *model.UserLoginRequest) (*model.LoginResponse, error) {
					if req.Username != testCase.requestBody.Username || req.Password != testCase.requestBody.Password {
						t.Errorf("LoginUser called with unexpected request: got %+v, want %+v", req, testCase.requestBody)
					}
					return testCase.mockFunc(req)
				}
			}

			router := setupRouter(mockUserService)

			jsonBody, _ := json.Marshal(testCase.requestBody)
			w := httptest.NewRecorder()
			req, _ := http.NewRequest("POST", "/api/v1/users/login", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")
			router.ServeHTTP(w, req)

			if w.Code != testCase.expectedStatus {
				t.Errorf("Expected status %d, got %d. Body: %s", testCase.expectedStatus, w.Code, w.Body.String())
			}

			var actualBody map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &actualBody)
			if err != nil {
				t.Fatalf("Failed to unmarshal actual response body: %v", err)
			}

			// 比较 error 字段
			if expectedErr, ok := testCase.expectedBody["error"]; ok {
				if actualErr, ok := actualBody["error"]; !ok || actualErr != expectedErr {
					t.Errorf("Expected error %q, got %q", expectedErr, actualErr)
				}
			} else if expectedMsg, ok := testCase.expectedBody["message"]; ok {
				// 比较 message 字段
				if actualMsg, ok := actualBody["message"]; !ok || actualMsg != expectedMsg {
					t.Errorf("Expected message %q, got %q", expectedMsg, actualMsg)
				}
				// 比较 data 字段 (仅在成功登录时)
				if expectedData, ok := testCase.expectedBody["data"].(map[string]interface{}); ok {
					if actualData, ok := actualBody["data"].(map[string]interface{}); ok {
						if actualData["token"] != expectedData["token"] {
							t.Errorf("Expected token %q, got %q", expectedData["token"], actualData["token"])
						}
						if expectedUser, ok := expectedData["user"].(map[string]interface{}); ok {
							if actualUser, ok := actualData["user"].(map[string]interface{}); ok {
								if actualUser["id"] != expectedUser["id"] ||
									actualUser["username"] != expectedUser["username"] ||
									actualUser["email"] != expectedUser["email"] {
									t.Errorf("Expected user %v, got %v", expectedUser, actualUser)
								}
							} else {
								t.Errorf("Expected user object in data, got %v", actualData["user"])
							}
						}
					} else {
						t.Errorf("Expected data object, got %v", actualBody["data"])
					}
				}
			}
		})
	}
}

func TestGetUserByID(t *testing.T) {
	tests := []struct {
		name           string
		userID         string
		mockFunc       func(id string) (*model.UserResponse, error)
		expectedStatus int
		expectedBody   map[string]interface{} // 修改为 map[string]interface{}
	}{
		{
			name:   "成功获取用户",
			userID: "user-123",
			mockFunc: func(id string) (*model.UserResponse, error) {
				return &model.UserResponse{
					ID:        id,
					Username:  "testuser",
					Email:     "<EMAIL>",
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil
			},
			expectedStatus: http.StatusOK,
			expectedBody: map[string]interface{}{
				"user": map[string]interface{}{
					"id":       "user-123",
					"username": "testuser",
					"email":    "<EMAIL>",
				},
			},
		},
		{
			name:           "用户未找到",
			userID:         "user-404",
			mockFunc: func(id string) (*model.UserResponse, error) {
				return nil, errors.New("user not found")
			},
			expectedStatus: http.StatusNotFound,
			expectedBody: map[string]interface{}{
				"error": "User not found",
			},
		},
		{
			name:           "无效用户ID",
			userID:         "", // 缺失ID
			mockFunc:       nil, // 不会调用 mock
			expectedStatus: http.StatusBadRequest,
			expectedBody: map[string]interface{}{
				"error": "User ID is required",
			},
		},
		{
			name:           "内部服务器错误",
			userID:         "user-500",
			mockFunc: func(id string) (*model.UserResponse, error) {
				return nil, errors.New("failed to get user")
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"error": "Failed to get user",
			},
		},
	}

	for _, tt := range tests {
		testCase := tt // 捕获当前 tt 的值
		t.Run(testCase.name, func(t *testing.T) {
			mockUserService := &MockUserService{
				T: t,
			}

			// 根据测试用例设置 mockFunc
			if testCase.mockFunc != nil {
				mockUserService.GetUserByIDFunc = func(id string) (*model.UserResponse, error) {
					if id != testCase.userID {
						t.Errorf("GetUserByID called with unexpected ID: got %q, want %q", id, testCase.userID)
					}
					return testCase.mockFunc(id)
				}
			}

			router := setupRouter(mockUserService)

			w := httptest.NewRecorder()
			req, _ := http.NewRequest("GET", "/api/v1/users/"+testCase.userID, nil) // 修正这里
			router.ServeHTTP(w, req)

			if w.Code != testCase.expectedStatus {
				t.Errorf("Expected status %d, got %d. Body: %s", testCase.expectedStatus, w.Code, w.Body.String())
			}

			var actualBody map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &actualBody)
			if err != nil {
				t.Fatalf("Failed to unmarshal actual response body: %v", err)
			}

			// 比较 error 字段
			if expectedErr, ok := testCase.expectedBody["error"]; ok {
				if actualErr, ok := actualBody["error"]; !ok || actualErr != expectedErr {
					t.Errorf("Expected error %q, got %q", expectedErr, actualErr)
				}
			} else if expectedUser, ok := testCase.expectedBody["user"].(map[string]interface{}); ok {
				// 比较 user 字段 (仅在成功获取用户时)
				if actualUser, ok := actualBody["user"].(map[string]interface{}); ok {
					if actualUser["id"] != expectedUser["id"] ||
						actualUser["username"] != expectedUser["username"] ||
						actualUser["email"] != expectedUser["email"] {
						t.Errorf("Expected user %v, got %v", expectedUser, actualUser)
					}
				} else {
					t.Errorf("Expected user object, got %v", actualBody["user"])
				}
			}
		})
	}
}
