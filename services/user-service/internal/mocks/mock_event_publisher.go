// Code generated by MockGen. DO NOT EDIT.
// Source: internal/event/event.go
//
// Generated by this command:
//
//	mockgen -source=internal/event/event.go -destination=internal/mocks/mock_event_publisher.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	event "pay-mall/services/user-service/internal/event"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockEventPublisher is a mock of EventPublisher interface.
type MockEventPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockEventPublisherMockRecorder
	isgomock struct{}
}

// MockEventPublisherMockRecorder is the mock recorder for MockEventPublisher.
type MockEventPublisherMockRecorder struct {
	mock *MockEventPublisher
}

// NewMockEventPublisher creates a new mock instance.
func NewMockEventPublisher(ctrl *gomock.Controller) *MockEventPublisher {
	mock := &MockEventPublisher{ctrl: ctrl}
	mock.recorder = &MockEventPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventPublisher) EXPECT() *MockEventPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockEventPublisher) Publish(arg0 *event.Event) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockEventPublisherMockRecorder) Publish(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockEventPublisher)(nil).Publish), arg0)
}
