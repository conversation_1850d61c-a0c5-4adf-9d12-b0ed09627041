package model

import (
	"database/sql"
	"time"
)

// User 与 sqlc 生成的结构体保持一致
// 只保留数据库字段

type User struct {
	ID           string       `json:"id" db:"id"`
	Username     string       `json:"username" db:"username"`
	Email        string       `json:"email" db:"email"`
	PasswordHash string       `json:"-" db:"password_hash"`
	CreatedAt    sql.NullTime `json:"created_at" db:"created_at"`
	UpdatedAt    sql.NullTime `json:"updated_at" db:"updated_at"`
}

// ToUserResponse 转换为 UserResponse
func (u *User) ToUserResponse() UserResponse {
	return UserResponse{
		ID:        u.ID,
		Username:  u.Username,
		Email:     u.Email,
		CreatedAt: u.CreatedAt.Time,
		UpdatedAt: u.UpdatedAt.Time,
	}
}

// SetCreatedAt 设置创建时间
func (u *User) SetCreatedAt(t time.Time) {
	u.CreatedAt = sql.NullTime{Time: t, Valid: true}
}

// SetUpdatedAt 设置更新时间
func (u *User) SetUpdatedAt(t time.Time) {
	u.UpdatedAt = sql.NullTime{Time: t, Valid: true}
}

// UserRegistrationRequest 用户注册请求
type UserRegistrationRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6,max=100"`
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID        string    `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string       `json:"token"`
	User      UserResponse `json:"user"`
	ExpiresAt time.Time    `json:"expires_at"`
}