package service

import (
	"testing"

	"pay-mall/services/user-service/internal/event"
	"pay-mall/services/user-service/internal/model"
)

// TestConfig 测试配置结构体
type TestConfig struct {
	JWT TestJWTConfig
}

// TestJWTConfig 测试JWT配置
type TestJWTConfig struct {
	Secret     string
	ExpireHour int
}

// GetJWTSecret 获取JWT密钥
func (c *TestConfig) GetJWTSecret() string {
	return c.JWT.Secret
}

// GetJWTExpireHour 获取JWT过期时间
func (c *TestConfig) GetJWTExpireHour() int {
	return c.JWT.ExpireHour
}

// MockUserRepository 模拟用户仓库
type MockUserRepository struct {
	users map[string]*model.User
}

func NewMockUserRepository() *MockUserRepository {
	return &MockUserRepository{
		users: make(map[string]*model.User),
	}
}

func (m *MockUserRepository) Create(user *model.User) error {
	m.users[user.ID] = user
	return nil
}

func (m *MockUserRepository) FindByID(id string) (*model.User, error) {
	if user, exists := m.users[id]; exists {
		return user, nil
	}
	return nil, nil
}

func (m *MockUserRepository) FindByUsername(username string) (*model.User, error) {
	for _, user := range m.users {
		if user.Username == username {
			return user, nil
		}
	}
	return nil, nil
}

func (m *MockUserRepository) FindByEmail(email string) (*model.User, error) {
	for _, user := range m.users {
		if user.Email == email {
			return user, nil
		}
	}
	return nil, nil
}

func (m *MockUserRepository) Update(user *model.User) error {
	if _, exists := m.users[user.ID]; exists {
		m.users[user.ID] = user
		return nil
	}
	return nil
}

func (m *MockUserRepository) Delete(id string) error {
	delete(m.users, id)
	return nil
}

func TestUserService_RegisterUser(t *testing.T) {
	// 创建模拟依赖
	mockRepo := NewMockUserRepository()
	mockEventPublisher := event.NewMockEventPublisher()
	
	// 创建测试配置
	testConfig := &TestConfig{
		JWT: TestJWTConfig{
			Secret:     "test-secret-key",
			ExpireHour: 24,
		},
	}
	
	// 创建服务实例
	service := NewUserService(mockRepo, mockEventPublisher, testConfig)

	// 测试用例
	tests := []struct {
		name    string
		req     *model.UserRegistrationRequest
		wantErr bool
	}{
		{
			name: "valid registration",
			req: &model.UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			wantErr: false,
		},
		{
			name: "duplicate username",
			req: &model.UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			wantErr: true,
		},
		{
			name: "duplicate email",
			req: &model.UserRegistrationRequest{
				Username: "testuser2",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := service.RegisterUser(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RegisterUser() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUserService_LoginUser(t *testing.T) {
	// 创建模拟依赖
	mockRepo := NewMockUserRepository()
	mockEventPublisher := event.NewMockEventPublisher()
	
	// 创建测试配置
	testConfig := &TestConfig{
		JWT: TestJWTConfig{
			Secret:     "test-secret-key",
			ExpireHour: 24,
		},
	}
	
	// 创建服务实例
	service := NewUserService(mockRepo, mockEventPublisher, testConfig)

	// 先注册一个用户
	registerReq := &model.UserRegistrationRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
	}
	_, err := service.RegisterUser(registerReq)
	if err != nil {
		t.Fatalf("Failed to register user for login test: %v", err)
	}

	// 测试用例
	tests := []struct {
		name    string
		req     *model.UserLoginRequest
		wantErr bool
	}{
		{
			name: "valid login",
			req: &model.UserLoginRequest{
				Username: "testuser",
				Password: "password123",
			},
			wantErr: false,
		},
		{
			name: "invalid password",
			req: &model.UserLoginRequest{
				Username: "testuser",
				Password: "wrongpassword",
			},
			wantErr: true,
		},
		{
			name: "user not found",
			req: &model.UserLoginRequest{
				Username: "nonexistent",
				Password: "password123",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := service.LoginUser(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("LoginUser() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
} 