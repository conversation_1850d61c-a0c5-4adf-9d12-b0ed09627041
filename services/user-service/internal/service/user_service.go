package service

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"pay-mall/services/user-service/internal/event"
	"pay-mall/services/user-service/internal/model"
	"pay-mall/services/user-service/internal/repository"

	"pay-mall/pkg/jwt"
	"pay-mall/services/user-service/internal/config" // 修改导入路径

	"golang.org/x/crypto/bcrypt"
)

// UserService 用户服务接口
type UserService interface {
	RegisterUser(req *model.UserRegistrationRequest) (*model.UserResponse, error)
	LoginUser(req *model.UserLoginRequest) (*model.LoginResponse, error)
	GetUserByID(id string) (*model.UserResponse, error)
	UpdateUser(id string, req *model.UserRegistrationRequest) (*model.UserResponse, error)
	DeleteUser(id string) error
	ListUsers(page, pageSize int) (*model.UserListResponse, error)
}

// ConfigProvider 配置提供者接口
type ConfigProvider interface {
	GetJWTSecret() string
	GetJWTExpireHour() int
}

// AppConfigAdapter 应用配置适配器
type AppConfigAdapter struct {
	config *config.Config // 修改类型
}

// NewAppConfigAdapter 创建应用配置适配器
func NewAppConfigAdapter(cfg *config.Config) ConfigProvider { // 修改参数类型
	return &AppConfigAdapter{config: cfg}
}

// GetJWTSecret 获取JWT密钥
func (a *AppConfigAdapter) GetJWTSecret() string {
	return a.config.JWT.Secret
}

// GetJWTExpireHour 获取JWT过期时间
func (a *AppConfigAdapter) GetJWTExpireHour() int {
	return a.config.JWT.ExpireHour
}

// userService 用户服务实现
type userService struct {
	userRepo       repository.UserRepository
	eventPublisher event.EventPublisher
	config         ConfigProvider
}

// NewUserService 创建用户服务实例
func NewUserService(userRepo repository.UserRepository, eventPublisher event.EventPublisher, config ConfigProvider) UserService {
	return &userService{
		userRepo:       userRepo,
		eventPublisher: eventPublisher,
		config:         config,
	}
}

// RegisterUser 用户注册
func (s *userService) RegisterUser(req *model.UserRegistrationRequest) (*model.UserResponse, error) {
	// 检查用户名是否已存在
	existingUser, err := s.userRepo.FindByUsername(req.Username)
	if err == nil && existingUser != nil {
		return nil, fmt.Errorf("username already exists")
	}

	// 检查邮箱是否已存在
	existingUser, err = s.userRepo.FindByEmail(req.Email)
	if err == nil && existingUser != nil {
		return nil, fmt.Errorf("email already exists")
	}

	// 生成密码哈希
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 生成用户ID
	userID, err := generateUserID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate user ID: %w", err)
	}

	// 创建用户
	user := &model.User{
		ID:           userID,
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: string(passwordHash),
	}

	// 设置时间
	now := time.Now()
	user.SetCreatedAt(now)
	user.SetUpdatedAt(now)

	if err := s.userRepo.Create(user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// 发布用户注册事件
	userRegisteredEvent := event.CreateUserRegisteredEvent(user.ID, user.Username, user.Email)
	if err := s.eventPublisher.Publish(&userRegisteredEvent); err != nil {
		// 记录错误但不影响用户注册流程
		fmt.Printf("Failed to publish user registered event: %v\n", err)
	}

	response := user.ToUserResponse()
	return &response, nil
}

// LoginUser 用户登录
func (s *userService) LoginUser(req *model.UserLoginRequest) (*model.LoginResponse, error) {
	// 查找用户
	user, err := s.userRepo.FindByUsername(req.Username)
	if err != nil {
		return nil, fmt.Errorf("invalid username or password")
	}

	// 检查用户是否存在
	if user == nil {
		return nil, fmt.Errorf("invalid username or password")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return nil, fmt.Errorf("invalid username or password")
	}

	// 生成JWT token
	token, err := jwt.GenerateToken(user.ID, s.config.GetJWTSecret(), s.config.GetJWTExpireHour())
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// 设置token过期时间
	expiresAt := time.Now().Add(time.Duration(s.config.GetJWTExpireHour()) * time.Hour)

	return &model.LoginResponse{
		Token:     token,
		User:      user.ToUserResponse(),
		ExpiresAt: expiresAt,
	}, nil
}

// GetUserByID 根据ID获取用户信息
func (s *userService) GetUserByID(id string) (*model.UserResponse, error) {
	user, err := s.userRepo.FindByID(id)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	response := user.ToUserResponse()
	return &response, nil
}

// UpdateUser 更新用户信息
func (s *userService) UpdateUser(id string, req *model.UserRegistrationRequest) (*model.UserResponse, error) {
	// 检查用户是否存在
	existingUser, err := s.userRepo.FindByID(id)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// 检查新用户名是否与其他用户冲突
	if req.Username != existingUser.Username {
		userWithUsername, err := s.userRepo.FindByUsername(req.Username)
		if err == nil && userWithUsername != nil {
			return nil, fmt.Errorf("username already exists")
		}
	}

	// 检查新邮箱是否与其他用户冲突
	if req.Email != existingUser.Email {
		userWithEmail, err := s.userRepo.FindByEmail(req.Email)
		if err == nil && userWithEmail != nil {
			return nil, fmt.Errorf("email already exists")
		}
	}

	// 生成新的密码哈希
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 更新用户信息
	existingUser.Username = req.Username
	existingUser.Email = req.Email
	existingUser.PasswordHash = string(passwordHash)
	existingUser.SetUpdatedAt(time.Now())

	if err := s.userRepo.Update(existingUser); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// 发布用户更新事件
	userUpdatedEvent := event.CreateUserUpdatedEvent(existingUser.ID, existingUser.Username, existingUser.Email)
	if err := s.eventPublisher.Publish(&userUpdatedEvent); err != nil {
		// 记录错误但不影响用户更新流程
		fmt.Printf("Failed to publish user updated event: %v\n", err)
	}

	response := existingUser.ToUserResponse()
	return &response, nil
}

// DeleteUser 删除用户
func (s *userService) DeleteUser(id string) error {
	// 检查用户是否存在
	existingUser, err := s.userRepo.FindByID(id)
	if err != nil {
		return fmt.Errorf("user not found: %w", err)
	}

	if err := s.userRepo.Delete(id); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	// 发布用户删除事件
	userDeletedEvent := event.CreateUserDeletedEvent(existingUser.ID)
	if err := s.eventPublisher.Publish(&userDeletedEvent); err != nil {
		// 记录错误但不影响用户删除流程
		fmt.Printf("Failed to publish user deleted event: %v\n", err)
	}

	return nil
}

// ListUsers 获取用户列表
func (s *userService) ListUsers(page, pageSize int) (*model.UserListResponse, error) {
	// 参数校验
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 计算偏移量
	offset := int32((page - 1) * pageSize)
	limit := int32(pageSize)

	// 获取用户列表
	users, err := s.userRepo.ListUsers(limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	// 获取总数
	total, err := s.userRepo.CountUsers()
	if err != nil {
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	// 转换为响应格式
	var userResponses []model.UserResponse
	for _, user := range users {
		userResponses = append(userResponses, user.ToUserResponse())
	}

	return &model.UserListResponse{
		Users:    userResponses,
		Page:     page,
		PageSize: pageSize,
		Total:    total,
	}, nil
}

// generateUserID 生成用户ID
func generateUserID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return "user-" + hex.EncodeToString(bytes), nil
}
