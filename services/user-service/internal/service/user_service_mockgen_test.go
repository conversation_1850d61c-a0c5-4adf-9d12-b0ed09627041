package service

import (
	"errors"
	"testing"
	"time"

	"pay-mall/services/user-service/internal/mocks"
	"pay-mall/services/user-service/internal/model"

	"go.uber.org/mock/gomock"
)

func TestUserService_RegisterUser_WithMockgen(t *testing.T) {
	tests := []struct {
		name        string
		req         *model.UserRegistrationRequest
		setupMocks  func(*mocks.MockUserRepository, *mocks.MockEventPublisher)
		wantErr     bool
		errContains string
	}{
		{
			name: "successful user registration",
			req: &model.UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			setupMocks: func(mockRepo *mocks.MockUserRepository, mockPublisher *mocks.MockEventPublisher) {
				// 检查用户名不存在
				mockRepo.EXPECT().
					FindByUsername("testuser").
					Return(nil, nil).
					Times(1)

				// 检查邮箱不存在
				mockRepo.EXPECT().
					FindByEmail("<EMAIL>").
					Return(nil, nil).
					Times(1)

				// 创建用户成功
				mockRepo.EXPECT().
					Create(gomock.Any()).
					Return(nil).
					Times(1)

				// 发布用户注册事件
				mockPublisher.EXPECT().
					Publish(gomock.Any()).
					Return(nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "username already exists",
			req: &model.UserRegistrationRequest{
				Username: "existinguser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			setupMocks: func(mockRepo *mocks.MockUserRepository, mockPublisher *mocks.MockEventPublisher) {
				// 用户名已存在
				existingUser := &model.User{
					ID:       "existing-user-id",
					Username: "existinguser",
					Email:    "<EMAIL>",
				}
				mockRepo.EXPECT().
					FindByUsername("existinguser").
					Return(existingUser, nil).
					Times(1)

				// 不应该调用其他方法
			},
			wantErr:     true,
			errContains: "username already exists",
		},
		{
			name: "email already exists",
			req: &model.UserRegistrationRequest{
				Username: "newuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			setupMocks: func(mockRepo *mocks.MockUserRepository, mockPublisher *mocks.MockEventPublisher) {
				// 用户名不存在
				mockRepo.EXPECT().
					FindByUsername("newuser").
					Return(nil, nil).
					Times(1)

				// 邮箱已存在
				existingUser := &model.User{
					ID:       "existing-user-id",
					Username: "existinguser",
					Email:    "<EMAIL>",
				}
				mockRepo.EXPECT().
					FindByEmail("<EMAIL>").
					Return(existingUser, nil).
					Times(1)

				// 不应该调用其他方法
			},
			wantErr:     true,
			errContains: "email already exists",
		},
		{
			name: "repository create error",
			req: &model.UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			setupMocks: func(mockRepo *mocks.MockUserRepository, mockPublisher *mocks.MockEventPublisher) {
				// 用户名和邮箱都不存在
				mockRepo.EXPECT().
					FindByUsername("testuser").
					Return(nil, nil).
					Times(1)

				mockRepo.EXPECT().
					FindByEmail("<EMAIL>").
					Return(nil, nil).
					Times(1)

				// 创建用户失败
				mockRepo.EXPECT().
					Create(gomock.Any()).
					Return(errors.New("database connection error")).
					Times(1)

				// 不应该发布事件
			},
			wantErr:     true,
			errContains: "database connection error",
		},
		{
			name: "event publish error should not fail registration",
			req: &model.UserRegistrationRequest{
				Username: "testuser",
				Email:    "<EMAIL>",
				Password: "password123",
			},
			setupMocks: func(mockRepo *mocks.MockUserRepository, mockPublisher *mocks.MockEventPublisher) {
				// 用户名和邮箱都不存在
				mockRepo.EXPECT().
					FindByUsername("testuser").
					Return(nil, nil).
					Times(1)

				mockRepo.EXPECT().
					FindByEmail("<EMAIL>").
					Return(nil, nil).
					Times(1)

				// 创建用户成功
				mockRepo.EXPECT().
					Create(gomock.Any()).
					Return(nil).
					Times(1)

				// 事件发布失败
				mockPublisher.EXPECT().
					Publish(gomock.Any()).
					Return(errors.New("event publish failed")).
					Times(1)
			},
			wantErr: false, // 事件发布失败不应该影响用户注册
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建gomock控制器
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// 创建Mock对象
			mockRepo := mocks.NewMockUserRepository(ctrl)
			mockPublisher := mocks.NewMockEventPublisher(ctrl)

			// 设置Mock期望
			if tt.setupMocks != nil {
				tt.setupMocks(mockRepo, mockPublisher)
			}

			// 创建测试配置
			testConfig := &TestConfig{
				JWT: TestJWTConfig{
					Secret:     "test-secret-key",
					ExpireHour: 24,
				},
			}

			// 创建服务实例
			service := NewUserService(mockRepo, mockPublisher, testConfig)

			// 执行测试
			result, err := service.RegisterUser(tt.req)

			// 验证结果
			if tt.wantErr {
				if err == nil {
					t.Errorf("RegisterUser() expected error, got nil")
				}
				if tt.errContains != "" && err != nil {
					if !contains(err.Error(), tt.errContains) {
						t.Errorf("RegisterUser() error = %v, want error containing %v", err, tt.errContains)
					}
				}
			} else {
				if err != nil {
					t.Errorf("RegisterUser() unexpected error = %v", err)
				}
				if result == nil {
					t.Error("RegisterUser() expected result, got nil")
				}
			}
		})
	}
}

func TestUserService_LoginUser_WithMockgen(t *testing.T) {
	// 预先计算密码哈希用于测试
	hashedPassword := "$2a$10$EATuPLtysw0lX0.Wz5jbce1FGSkKgiHmvowahi8Z1PDUUupClpoTm" // "password123"

	tests := []struct {
		name        string
		req         *model.UserLoginRequest
		setupMocks  func(*mocks.MockUserRepository, *mocks.MockEventPublisher)
		wantErr     bool
		errContains string
	}{
		{
			name: "successful login",
			req: &model.UserLoginRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(mockRepo *mocks.MockUserRepository, mockPublisher *mocks.MockEventPublisher) {
				// 找到用户
				user := &model.User{
					ID:           "user-123",
					Username:     "testuser",
					Email:        "<EMAIL>",
					PasswordHash: hashedPassword,
				}
				user.SetCreatedAt(time.Now())
				user.SetUpdatedAt(time.Now())

				mockRepo.EXPECT().
					FindByUsername("testuser").
					Return(user, nil).
					Times(1)
			},
			wantErr: false,
		},
		{
			name: "user not found",
			req: &model.UserLoginRequest{
				Username: "nonexistent",
				Password: "password123",
			},
			setupMocks: func(mockRepo *mocks.MockUserRepository, mockPublisher *mocks.MockEventPublisher) {
				// 用户不存在
				mockRepo.EXPECT().
					FindByUsername("nonexistent").
					Return(nil, nil).
					Times(1)
			},
			wantErr:     true,
			errContains: "invalid username or password",
		},
		{
			name: "invalid password",
			req: &model.UserLoginRequest{
				Username: "testuser",
				Password: "wrongpassword",
			},
			setupMocks: func(mockRepo *mocks.MockUserRepository, mockPublisher *mocks.MockEventPublisher) {
				// 找到用户但密码错误
				user := &model.User{
					ID:           "user-123",
					Username:     "testuser",
					Email:        "<EMAIL>",
					PasswordHash: hashedPassword,
				}
				user.SetCreatedAt(time.Now())
				user.SetUpdatedAt(time.Now())

				mockRepo.EXPECT().
					FindByUsername("testuser").
					Return(user, nil).
					Times(1)
			},
			wantErr:     true,
			errContains: "invalid username or password",
		},
		{
			name: "repository error",
			req: &model.UserLoginRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(mockRepo *mocks.MockUserRepository, mockPublisher *mocks.MockEventPublisher) {
				// 数据库错误 - 但服务层会将其转换为通用错误消息
				mockRepo.EXPECT().
					FindByUsername("testuser").
					Return(nil, errors.New("database connection failed")).
					Times(1)
			},
			wantErr:     true,
			errContains: "invalid username or password", // 服务层出于安全考虑统一返回此错误
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建gomock控制器
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// 创建Mock对象
			mockRepo := mocks.NewMockUserRepository(ctrl)
			mockPublisher := mocks.NewMockEventPublisher(ctrl)

			// 设置Mock期望
			if tt.setupMocks != nil {
				tt.setupMocks(mockRepo, mockPublisher)
			}

			// 创建测试配置
			testConfig := &TestConfig{
				JWT: TestJWTConfig{
					Secret:     "test-secret-key",
					ExpireHour: 24,
				},
			}

			// 创建服务实例
			service := NewUserService(mockRepo, mockPublisher, testConfig)

			// 执行测试
			result, err := service.LoginUser(tt.req)

			// 验证结果
			if tt.wantErr {
				if err == nil {
					t.Errorf("LoginUser() expected error, got nil")
				}
				if tt.errContains != "" && err != nil {
					if !contains(err.Error(), tt.errContains) {
						t.Errorf("LoginUser() error = %v, want error containing %v", err, tt.errContains)
					}
				}
			} else {
				if err != nil {
					t.Errorf("LoginUser() unexpected error = %v", err)
				}
				if result == nil {
					t.Error("LoginUser() expected result, got nil")
				}
			}
		})
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && s[:len(substr)] == substr) ||
		(len(s) > len(substr) && s[len(s)-len(substr):] == substr) ||
		containsHelper(s, substr))
}

func containsHelper(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
