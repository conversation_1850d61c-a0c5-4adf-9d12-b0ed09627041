package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 应用配置
type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	Redis    RedisConfig    `yaml:"redis"`
	Log      LogConfig      `yaml:"log"`
	JWT      JWTConfig      `yaml:"jwt"`   // 新增 JWT 配置
	Kafka    KafkaConfig    `yaml:"kafka"` // 新增 Kafka 配置
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `yaml:"port"`
	Host string `yaml:"host"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Database string `yaml:"database"`
	SSLMode  string `yaml:"ssl_mode"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Password string `yaml:"password"`
	Database int    `yaml:"database"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	File   string `yaml:"file"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret     string `yaml:"secret"`
	ExpireHour int    `yaml:"expire_hour"`
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Brokers []string `yaml:"brokers"`
	Topic   string   `yaml:"topic"`
	Enabled bool     `yaml:"enabled"`
}

// LoadConfig 加载配置
func LoadConfig(configPath string) (*Config, error) {
	var cfg Config

	// 确定配置文件路径
	if configPath == "" {
		env := os.Getenv("APP_ENV")
		if env == "" {
			env = "local" // 默认环境为 local
		}
		// 假设配置文件在项目根目录下的 configs 文件夹
		configPath = fmt.Sprintf("user-service/configs/%s.yaml", env)
	}

	// 读取文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", configPath, err)
	}

	// 解析 YAML
	err = yaml.Unmarshal(data, &cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config file %s: %w", configPath, err)
	}

	return &cfg, nil
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		c.Username, c.Password, c.Host, c.Port, c.Database)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetKafkaBrokers 获取Kafka代理地址
func (c *Config) GetKafkaBrokers() []string {
	if c.Kafka.Brokers == nil || len(c.Kafka.Brokers) == 0 {
		return []string{"localhost:9092"} // 默认值
	}
	return c.Kafka.Brokers
}

// GetKafkaTopic 获取Kafka主题
func (c *Config) GetKafkaTopic() string {
	if c.Kafka.Topic == "" {
		return "pay-mall-events" // 默认主题
	}
	return c.Kafka.Topic
}

// IsKafkaEnabled 检查Kafka是否启用
func (c *Config) IsKafkaEnabled() bool {
	return c.Kafka.Enabled
}
