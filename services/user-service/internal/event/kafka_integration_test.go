package event

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"pay-mall/pkg/kafka"

	"github.com/segmentio/kafka-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const (
	testKafkaBroker = "localhost:9092"
	testKafkaTopic  = "user-service-test-topic"
	testGroupID     = "user-service-test-group"
)

// setupKafkaTestTopic 创建测试主题
func setupKafkaTestTopic(t *testing.T) {
	conn, err := kafka.Dial("tcp", testKafkaBroker)
	require.NoError(t, err)
	defer conn.Close()

	controller, err := conn.Controller()
	require.NoError(t, err)

	controllerConn, err := kafka.Dial("tcp", fmt.Sprintf("%s:%d", controller.Host, controller.Port))
	require.NoError(t, err)
	defer controllerConn.Close()

	topicConfigs := []kafka.TopicConfig{
		{
			Topic:             testKafkaTopic,
			NumPartitions:     3,
			ReplicationFactor: 1,
		},
	}

	err = controllerConn.CreateTopics(topicConfigs...)
	if err != nil {
		t.Logf("Topic creation warning: %v", err)
	}
}

// cleanupKafkaTestTopic 清理测试主题
func cleanupKafkaTestTopic(t *testing.T) {
	conn, err := kafka.Dial("tcp", testKafkaBroker)
	if err != nil {
		t.Logf("Failed to connect for cleanup: %v", err)
		return
	}
	defer conn.Close()

	controller, err := conn.Controller()
	if err != nil {
		t.Logf("Failed to get controller for cleanup: %v", err)
		return
	}

	controllerConn, err := kafka.Dial("tcp", fmt.Sprintf("%s:%d", controller.Host, controller.Port))
	if err != nil {
		t.Logf("Failed to connect to controller for cleanup: %v", err)
		return
	}
	defer controllerConn.Close()

	err = controllerConn.DeleteTopics(testKafkaTopic)
	if err != nil {
		t.Logf("Failed to delete topic: %v", err)
	}
}

// TestKafkaEventPublisherAdapter_Integration 测试Kafka事件发布器适配器集成
func TestKafkaEventPublisherAdapter_Integration(t *testing.T) {
	setupKafkaTestTopic(t)
	defer cleanupKafkaTestTopic(t)

	// 创建Kafka事件发布器
	kafkaPublisher, err := kafka.NewKafkaEventPublisher(
		[]string{testKafkaBroker},
		testKafkaTopic,
		"user-service",
	)
	require.NoError(t, err)

	// 创建适配器
	adapter := NewKafkaEventPublisherAdapter(kafkaPublisher)

	// 创建消费者来验证消息
	consumer, err := kafka.NewConsumer(
		[]string{testKafkaBroker},
		testKafkaTopic,
		testGroupID,
	)
	require.NoError(t, err)

	// 测试用户注册事件
	userEvent := CreateUserRegisteredEvent("user-123", "testuser", "<EMAIL>")
	err = adapter.Publish(userEvent)
	require.NoError(t, err)

	// 等待消息传播
	time.Sleep(2 * time.Second)

	// 消费并验证消息
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	message, err := consumer.ReadMessage(ctx)
	require.NoError(t, err)

	// 验证分区键
	assert.Equal(t, "user-user-123", string(message.Key))

	// 验证消息内容
	var receivedEvent kafka.Event
	err = json.Unmarshal(message.Value, &receivedEvent)
	require.NoError(t, err)

	assert.Equal(t, "user.registered", receivedEvent.Type)
	assert.Equal(t, "user-service", receivedEvent.Source)
	assert.Equal(t, "1.0", receivedEvent.Version)

	// 验证事件数据
	data, ok := receivedEvent.Data.(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "user-123", data["user_id"])
	assert.Equal(t, "testuser", data["username"])
	assert.Equal(t, "<EMAIL>", data["email"])
}

// TestUserEventTypes_Integration 测试所有用户事件类型的集成
func TestUserEventTypes_Integration(t *testing.T) {
	setupKafkaTestTopic(t)
	defer cleanupKafkaTestTopic(t)

	// 创建Kafka事件发布器
	kafkaPublisher, err := kafka.NewKafkaEventPublisher(
		[]string{testKafkaBroker},
		testKafkaTopic,
		"user-service",
	)
	require.NoError(t, err)

	// 创建适配器
	adapter := NewKafkaEventPublisherAdapter(kafkaPublisher)

	// 创建消费者
	consumer, err := kafka.NewConsumer(
		[]string{testKafkaBroker},
		testKafkaTopic,
		fmt.Sprintf("%s-all-events", testGroupID),
	)
	require.NoError(t, err)

	// 测试事件列表
	testEvents := []struct {
		name  string
		event Event
	}{
		{
			name:  "UserRegistered",
			event: CreateUserRegisteredEvent("user-001", "user1", "<EMAIL>"),
		},
		{
			name:  "UserUpdated",
			event: CreateUserUpdatedEvent("user-002", "user2", "<EMAIL>"),
		},
		{
			name:  "UserDeleted",
			event: CreateUserDeletedEvent("user-003"),
		},
	}

	// 发布所有事件
	for _, testEvent := range testEvents {
		err = adapter.Publish(testEvent.event)
		require.NoError(t, err, "Failed to publish %s event", testEvent.name)
	}

	// 等待消息传播
	time.Sleep(3 * time.Second)

	// 消费并验证所有消息
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	receivedEvents := make([]kafka.Event, 0, len(testEvents))
	for i := 0; i < len(testEvents); i++ {
		message, err := consumer.ReadMessage(ctx)
		require.NoError(t, err, "Failed to read message %d", i)

		var event kafka.Event
		err = json.Unmarshal(message.Value, &event)
		require.NoError(t, err, "Failed to unmarshal message %d", i)

		receivedEvents = append(receivedEvents, event)
	}

	// 验证接收到的事件数量
	assert.Len(t, receivedEvents, len(testEvents))

	// 验证事件类型
	eventTypes := make(map[string]bool)
	for _, event := range receivedEvents {
		eventTypes[event.Type] = true
		assert.Equal(t, "user-service", event.Source)
		assert.Equal(t, "1.0", event.Version)
		assert.NotEmpty(t, event.ID)
		assert.False(t, event.Timestamp.IsZero())
	}

	// 验证所有预期的事件类型都被接收
	assert.True(t, eventTypes["user.registered"], "user.registered event not received")
	assert.True(t, eventTypes["user.updated"], "user.updated event not received")
	assert.True(t, eventTypes["user.deleted"], "user.deleted event not received")
}

// TestKafkaEventPublisher_ErrorHandling 测试Kafka事件发布器错误处理
func TestKafkaEventPublisher_ErrorHandling(t *testing.T) {
	// 使用无效的broker地址测试错误处理
	invalidBrokers := []string{"invalid-broker:9092"}
	
	// 创建事件发布器（这应该成功，因为连接是延迟的）
	kafkaPublisher, err := kafka.NewKafkaEventPublisher(
		invalidBrokers,
		testKafkaTopic,
		"user-service",
	)
	require.NoError(t, err)

	adapter := NewKafkaEventPublisherAdapter(kafkaPublisher)

	// 尝试发布事件（这应该失败）
	userEvent := CreateUserRegisteredEvent("user-error", "erroruser", "<EMAIL>")
	err = adapter.Publish(userEvent)
	
	// 应该返回错误，因为无法连接到无效的broker
	assert.Error(t, err)
	t.Logf("Expected error occurred: %v", err)
}

// TestKafkaEventPublisher_PartitionKeys 测试分区键生成
func TestKafkaEventPublisher_PartitionKeys(t *testing.T) {
	setupKafkaTestTopic(t)
	defer cleanupKafkaTestTopic(t)

	// 创建Kafka事件发布器
	kafkaPublisher, err := kafka.NewKafkaEventPublisher(
		[]string{testKafkaBroker},
		testKafkaTopic,
		"user-service",
	)
	require.NoError(t, err)

	adapter := NewKafkaEventPublisherAdapter(kafkaPublisher)

	// 创建消费者
	consumer, err := kafka.NewConsumer(
		[]string{testKafkaBroker},
		testKafkaTopic,
		fmt.Sprintf("%s-partition-test", testGroupID),
	)
	require.NoError(t, err)

	// 测试不同用户ID的分区键
	userIDs := []string{"user-001", "user-002", "user-003"}
	
	for _, userID := range userIDs {
		userEvent := CreateUserRegisteredEvent(userID, "testuser", "<EMAIL>")
		err = adapter.Publish(userEvent)
		require.NoError(t, err)
	}

	// 等待消息传播
	time.Sleep(2 * time.Second)

	// 验证分区键
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	for i, expectedUserID := range userIDs {
		message, err := consumer.ReadMessage(ctx)
		require.NoError(t, err, "Failed to read message %d", i)

		expectedKey := fmt.Sprintf("user-%s", expectedUserID)
		assert.Equal(t, expectedKey, string(message.Key), "Partition key mismatch for user %s", expectedUserID)
	}
}
