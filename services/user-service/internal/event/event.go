package event

import (
	"encoding/json"
	"fmt"
	"time"
)

// EventType 事件类型
type EventType string

const (
	UserRegistered EventType = "UserRegistered"
	UserUpdated    EventType = "UserUpdated"
	UserDeleted    EventType = "UserDeleted"
)

// Event 事件结构
type Event struct {
	ID        string                 `json:"id"`
	Type      EventType              `json:"type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
}

// EventPublisher 事件发布器接口
type EventPublisher interface {
	Publish(event *Event) error
}

// MockEventPublisher 模拟事件发布器 (用于开发阶段)
type MockEventPublisher struct{}

// NewMockEventPublisher 创建模拟事件发布器
func NewMockEventPublisher() EventPublisher {
	return &MockEventPublisher{}
}

// Publish 发布事件 (模拟实现)
func (p *MockEventPublisher) Publish(event *Event) error {
	// 在实际项目中，这里会发送到消息队列 (Kafka/RabbitMQ)
	eventJSON, _ := json.MarshalIndent(event, "", "  ")
	fmt.Printf("Event published: %s\n", string(eventJSON))
	return nil
}

// KafkaEventPublisherAdapter Kafka事件发布器适配器
type KafkaEventPublisherAdapter struct {
	kafkaPublisher interface {
		PublishUserEvent(eventType string, data map[string]interface{}) error
	}
}

// NewKafkaEventPublisherAdapter 创建Kafka事件发布器适配器
func NewKafkaEventPublisherAdapter(kafkaPublisher interface {
	PublishUserEvent(eventType string, data map[string]interface{}) error
}) EventPublisher {
	return &KafkaEventPublisherAdapter{
		kafkaPublisher: kafkaPublisher,
	}
}

// Publish 发布事件到Kafka
func (k *KafkaEventPublisherAdapter) Publish(event *Event) error {
	return k.kafkaPublisher.PublishUserEvent(string(event.Type), event.Data)
}

// CreateUserRegisteredEvent 创建用户注册事件
func CreateUserRegisteredEvent(userID, username, email string) Event {
	return Event{
		ID:   fmt.Sprintf("event-%d", time.Now().UnixNano()),
		Type: UserRegistered,
		Data: map[string]interface{}{
			"user_id":  userID,
			"username": username,
			"email":    email,
		},
		Timestamp: time.Now(),
		Source:    "user-service",
	}
}

// CreateUserUpdatedEvent 创建用户更新事件
func CreateUserUpdatedEvent(userID, username, email string) Event {
	return Event{
		ID:   fmt.Sprintf("event-%d", time.Now().UnixNano()),
		Type: UserUpdated,
		Data: map[string]interface{}{
			"user_id":  userID,
			"username": username,
			"email":    email,
		},
		Timestamp: time.Now(),
		Source:    "user-service",
	}
}

// CreateUserDeletedEvent 创建用户删除事件
func CreateUserDeletedEvent(userID string) Event {
	return Event{
		ID:   fmt.Sprintf("event-%d", time.Now().UnixNano()),
		Type: UserDeleted,
		Data: map[string]interface{}{
			"user_id": userID,
		},
		Timestamp: time.Now(),
		Source:    "user-service",
	}
}
