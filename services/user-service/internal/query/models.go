// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package query

import (
	"database/sql"
	"encoding/json"
)

type Event struct {
	ID        string
	EventType string
	EventData json.RawMessage
	Source    string
	Timestamp sql.NullTime
	Processed sql.NullBool
}

type User struct {
	ID           string
	Username     string
	Email        string
	PasswordHash string
	CreatedAt    sql.NullTime
	UpdatedAt    sql.NullTime
}
