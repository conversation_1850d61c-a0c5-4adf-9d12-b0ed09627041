package repository

import (
	"context"
	"database/sql"

	"pay-mall/services/user-service/internal/model"
	"pay-mall/services/user-service/internal/query"
)

// UserRepository 用户数据访问接口
type UserRepository interface {
	Create(user *model.User) error
	FindByID(id string) (*model.User, error)
	FindByUsername(username string) (*model.User, error)
	FindByEmail(email string) (*model.User, error)
	Update(user *model.User) error
	Delete(id string) error
}

// userRepository 用户仓库实现
type userRepository struct {
	q *query.Queries
}

// NewUserRepository 创建用户仓库实例
func NewUserRepository(db *sql.DB) UserRepository {
	return &userRepository{q: query.New(db)}
}

// Create 创建用户
func (r *userRepository) Create(user *model.User) error {
	ctx := context.Background()
	return r.q.CreateUser(ctx, query.CreateUserParams{
		ID:           user.ID,
		Username:     user.Username,
		Email:        user.Email,
		PasswordHash: user.PasswordHash,
		CreatedAt:    user.CreatedAt,
		UpdatedAt:    user.UpdatedAt,
	})
}

// FindByID 根据ID查找用户
func (r *userRepository) FindByID(id string) (*model.User, error) {
	ctx := context.Background()
	u, err := r.q.GetUserByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return &model.User{
		ID:           u.ID,
		Username:     u.Username,
		Email:        u.Email,
		PasswordHash: u.PasswordHash,
		CreatedAt:    u.CreatedAt,
		UpdatedAt:    u.UpdatedAt,
	}, nil
}

// FindByUsername 根据用户名查找用户
func (r *userRepository) FindByUsername(username string) (*model.User, error) {
	ctx := context.Background()
	u, err := r.q.GetUserByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	return &model.User{
		ID:           u.ID,
		Username:     u.Username,
		Email:        u.Email,
		PasswordHash: u.PasswordHash,
		CreatedAt:    u.CreatedAt,
		UpdatedAt:    u.UpdatedAt,
	}, nil
}

// FindByEmail 根据邮箱查找用户
func (r *userRepository) FindByEmail(email string) (*model.User, error) {
	ctx := context.Background()
	u, err := r.q.GetUserByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	return &model.User{
		ID:           u.ID,
		Username:     u.Username,
		Email:        u.Email,
		PasswordHash: u.PasswordHash,
		CreatedAt:    u.CreatedAt,
		UpdatedAt:    u.UpdatedAt,
	}, nil
}

// Update 更新用户信息
func (r *userRepository) Update(user *model.User) error {
	ctx := context.Background()
	return r.q.UpdateUser(ctx, query.UpdateUserParams{
		Username:     user.Username,
		Email:        user.Email,
		PasswordHash: user.PasswordHash,
		UpdatedAt:    user.UpdatedAt,
		ID:           user.ID,
	})
}

// Delete 删除用户
func (r *userRepository) Delete(id string) error {
	ctx := context.Background()
	return r.q.DeleteUser(ctx, id)
} 