.PHONY: help build run test clean docker-build docker-run docker-stop deps lint

# 默认目标
help:
	@echo "可用的命令:"
	@echo "  build        - 构建应用"
	@echo "  run          - 运行应用"
	@echo "  test         - 运行测试"
	@echo "  clean        - 清理构建文件"
	@echo "  deps         - 下载依赖"
	@echo "  lint         - 代码检查"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run   - 使用Docker Compose运行"
	@echo "  docker-stop  - 停止Docker服务"

# 下载依赖
deps:
	go mod download
	go mod tidy

# 构建应用
build: deps
	go build -o bin/user-service cmd/main.go

# 运行应用
run: build
	./bin/user-service

# 运行测试
test:
	go test -v ./...

# 清理构建文件
clean:
	rm -rf bin/
	rm -f coverage.out coverage.html

# 构建Docker镜像
docker-build:
	docker build -t user-service .

# 使用Docker Compose运行
docker-run:
	docker-compose up -d

# 停止Docker服务
docker-stop:
	docker-compose down

# 查看Docker日志
docker-logs:
	docker-compose logs -f user-service

# 格式化代码
fmt:
	go fmt ./... 