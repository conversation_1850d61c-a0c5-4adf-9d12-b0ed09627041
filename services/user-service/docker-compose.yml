version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: user-service-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: user_service
      MYSQL_USER: user
      MYSQL_PASSWORD: userpassword
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - user-service-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: user-service-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - user-service-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5

  # 用户服务
  user-service:
    build: .
    container_name: user-service
    environment:
      - SERVER_PORT=8081
      - SERVER_HOST=0.0.0.0
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=user
      - DB_PASSWORD=userpassword
      - DB_DATABASE=user_service
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DATABASE=0
    ports:
      - "8081:8081"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - user-service-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  user-service-network:
    driver: bridge 