# User Service

用户服务是支付商城系统的核心微服务之一，负责用户注册、登录、信息管理等功能。

## 功能特性

- 用户注册
- 用户登录
- 用户信息查询
- 用户信息更新
- 用户删除
- 事件驱动架构支持
- 密码安全哈希
- 数据库持久化

## 技术栈

- **语言**: Go 1.24.4
- **Web框架**: Gin
- **数据库**: MySQL
- **密码哈希**: bcrypt
- **事件发布**: 模拟实现 (可扩展为 Kafka/RabbitMQ)

## 项目结构

```
user-service/
├── cmd/
│   └── main.go              # 应用入口
├── internal/
│   ├── config/
│   │   └── config.go        # 配置管理
│   ├── database/
│   │   └── database.go      # 数据库连接管理
│   ├── event/
│   │   └── event.go         # 事件发布
│   ├── handler/
│   │   └── user_handler.go  # HTTP处理器
│   ├── model/
│   │   └── user.go          # 数据模型
│   ├── repository/
│   │   └── user_repository.go # 数据访问层
│   └── service/
│       └── user_service.go  # 业务逻辑层
├── go.mod                   # Go模块文件
├── go.sum                   # 依赖校验文件
└── README.md               # 项目文档
```

## 快速开始

### 环境要求

- Go 1.24.4+
- MySQL 8.0+

### 环境变量配置

```bash
# 服务器配置
SERVER_PORT=8081
SERVER_HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=user_service

# Redis配置 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
```

### 安装依赖

```bash
go mod tidy
```

### 运行服务

```bash
go run cmd/main.go
```

服务将在 `http://localhost:8081` 启动。

## API 接口

### 基础路径

所有API接口都以 `/api/v1` 为基础路径。

### 健康检查

```
GET /health
```

响应示例:
```json
{
  "status": "ok",
  "service": "user-service",
  "timestamp": **********
}
```

### 用户注册

```
POST /api/v1/users/register
```

请求体:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

响应示例:
```json
{
  "message": "User registered successfully",
  "user": {
    "id": "user-abc123",
    "username": "testuser",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 用户登录

```
POST /api/v1/users/login
```

请求体:
```json
{
  "username": "testuser",
  "password": "password123"
}
```

响应示例:
```json
{
  "message": "Login successful",
  "data": {
    "token": "token-xyz789",
    "user": {
      "id": "user-abc123",
      "username": "testuser",
      "email": "<EMAIL>",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    },
    "expires_at": "2024-01-02T00:00:00Z"
  }
}
```

### 获取用户信息

```
GET /api/v1/users/{id}
```

响应示例:
```json
{
  "user": {
    "id": "user-abc123",
    "username": "testuser",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 更新用户信息

```
PUT /api/v1/users/{id}
```

请求体:
```json
{
  "username": "newusername",
  "email": "<EMAIL>",
  "password": "newpassword123"
}
```

### 删除用户

```
DELETE /api/v1/users/{id}
```

### 获取用户列表

```
GET /api/v1/users?page=1&page_size=10
```

## 事件发布

用户服务会发布以下事件:

### UserRegistered 事件

当用户注册成功时发布:

```json
{
  "id": "event-**********000000000",
  "type": "UserRegistered",
  "data": {
    "user_id": "user-abc123",
    "username": "testuser",
    "email": "<EMAIL>"
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "source": "user-service"
}
```

### UserUpdated 事件

当用户信息更新时发布:

```json
{
  "id": "event-**********000000000",
  "type": "UserUpdated",
  "data": {
    "user_id": "user-abc123",
    "username": "newusername",
    "email": "<EMAIL>"
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "source": "user-service"
}
```

### UserDeleted 事件

当用户被删除时发布:

```json
{
  "id": "event-**********000000000",
  "type": "UserDeleted",
  "data": {
    "user_id": "user-abc123"
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "source": "user-service"
}
```

## 数据库设计

### users 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | VARCHAR(36) | 用户唯一ID，主键 |
| username | VARCHAR(50) | 用户名，唯一 |
| email | VARCHAR(100) | 邮箱地址，唯一 |
| password_hash | VARCHAR(255) | 密码哈希值 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

索引:
- `idx_username` (username)
- `idx_email` (email)
- `idx_created_at` (created_at)

## 错误处理

服务使用标准的HTTP状态码:

- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 认证失败
- `404 Not Found`: 资源不存在
- `409 Conflict`: 资源冲突 (如用户名已存在)
- `500 Internal Server Error`: 服务器内部错误

## 开发说明

### 添加新功能

1. 在 `model/` 中定义数据模型
2. 在 `repository/` 中实现数据访问
3. 在 `service/` 中实现业务逻辑
4. 在 `handler/` 中实现HTTP接口
5. 在 `main.go` 中注册路由

### 测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/service
```

### 构建

```bash
# 构建可执行文件
go build -o user-service cmd/main.go

# 运行构建后的文件
./user-service
```

## 部署

### Docker 部署

```dockerfile
FROM golang:1.24.4-alpine AS builder

WORKDIR /app
COPY . .
RUN go mod download
RUN go build -o user-service cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/user-service .
CMD ["./user-service"]
```

### Kubernetes 部署

参考 `k8s/` 目录下的配置文件。

## 监控和日志

- 健康检查端点: `/health`
- 日志格式: JSON
- 建议集成 Prometheus + Grafana 进行监控

## 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License 