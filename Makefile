# Pay-Mall Kafka测试 Makefile

.PHONY: help kafka-start kafka-stop kafka-test kafka-unit kafka-integration kafka-cleanup docker-check

# 默认目标
help:
	@echo "Pay-Mall Kafka测试命令:"
	@echo ""
	@echo "  make kafka-start      - 启动Kafka服务"
	@echo "  make kafka-stop       - 停止Kafka服务"
	@echo "  make kafka-test       - 运行所有Kafka测试"
	@echo "  make kafka-unit       - 运行Kafka单元测试"
	@echo "  make kafka-integration - 运行Kafka集成测试"
	@echo "  make kafka-cleanup    - 清理Kafka测试环境"
	@echo "  make docker-check     - 检查Docker状态"
	@echo ""
	@echo "  make test-all         - 运行所有测试（包括现有的单元测试）"
	@echo "  make build-all        - 构建所有服务"
	@echo ""

# Docker检查
docker-check:
	@echo "检查Docker状态..."
	@docker info > /dev/null 2>&1 || (echo "错误: Docker未运行，请启动Docker Desktop" && exit 1)
	@echo "✅ Docker运行正常"

# 启动Kafka
kafka-start: docker-check
	@echo "🚀 启动Kafka服务..."
	@docker compose up -d broker
	@echo "⏳ 等待Kafka启动..."
	@timeout 60 bash -c 'until docker logs broker 2>&1 | grep -q "Kafka Server started"; do sleep 2; done' || (echo "❌ Kafka启动超时" && exit 1)
	@echo "✅ Kafka启动成功"

# 停止Kafka
kafka-stop: docker-check
	@echo "🛑 停止Kafka服务..."
	@docker compose down -v
	@echo "✅ Kafka服务已停止"

# 运行Kafka单元测试
kafka-unit:
	@echo "🧪 运行Kafka单元测试..."
	@cd pkg && go test -v ./kafka/... -run "Test.*" -short
	@echo "✅ Kafka单元测试完成"

# 运行Kafka集成测试
kafka-integration: kafka-start
	@echo "🔗 运行Kafka集成测试..."
	@sleep 5  # 等待Kafka完全就绪
	@echo "测试用户服务Kafka集成..."
	@cd services/user-service && go test -v ./internal/event/... -run ".*Integration.*"
	@echo "测试订单服务Kafka集成..."
	@cd services/order-service && go test -v ./internal/event/... -run ".*Integration.*"
	@echo "✅ Kafka集成测试完成"

# 运行所有Kafka测试
kafka-test: kafka-unit kafka-integration
	@echo "🎉 所有Kafka测试完成"

# 清理Kafka环境
kafka-cleanup: docker-check
	@echo "🧹 清理Kafka测试环境..."
	@docker compose down -v
	@docker system prune -f --volumes || true
	@echo "✅ 清理完成"

# 运行所有现有测试
test-existing:
	@echo "🧪 运行现有单元测试..."
	@cd services/user-service && go test -v ./...
	@cd services/order-service && go test -v ./...
	@echo "✅ 现有测试完成"

# 运行所有测试（现有 + Kafka）
test-all: test-existing kafka-test
	@echo "🎉 所有测试完成"

# 构建所有服务
build-all:
	@echo "🔨 构建所有服务..."
	@cd services/user-service && go build -o ../../bin/user-service ./cmd/main.go
	@cd services/order-service && go build -o ../../bin/order-service ./cmd/main.go
	@echo "✅ 构建完成"

# 安装依赖
deps:
	@echo "📦 安装依赖..."
	@cd pkg && go mod tidy
	@cd services/user-service && go mod tidy
	@cd services/order-service && go mod tidy
	@echo "✅ 依赖安装完成"

# 格式化代码
fmt:
	@echo "🎨 格式化代码..."
	@go fmt ./...
	@cd services/user-service && go fmt ./...
	@cd services/order-service && go fmt ./...
	@echo "✅ 代码格式化完成"

# 代码检查
lint:
	@echo "🔍 代码检查..."
	@golangci-lint run ./... || echo "请安装 golangci-lint: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"
	@echo "✅ 代码检查完成"

# 开发环境设置
dev-setup: deps
	@echo "🛠️ 设置开发环境..."
	@mkdir -p bin
	@mkdir -p logs
	@echo "✅ 开发环境设置完成"

# 快速测试（仅单元测试）
quick-test:
	@echo "⚡ 快速测试（仅单元测试）..."
	@$(MAKE) kafka-unit
	@$(MAKE) test-existing
	@echo "✅ 快速测试完成"

# 完整测试（包括集成测试）
full-test: kafka-test test-existing
	@echo "🎯 完整测试完成"

# 清理所有
clean-all: kafka-cleanup
	@echo "🧹 清理所有..."
	@rm -rf bin/
	@rm -rf logs/
	@go clean -cache
	@echo "✅ 清理完成"
