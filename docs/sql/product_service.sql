CREATE TABLE `categories` (
    `id` VARCHAR(36) PRIMARY KEY NOT NULL,
    `name` VA<PERSON>HA<PERSON>(100) UNIQUE NOT NULL,
    `description` TEXT
);

CREATE TABLE `products` (
    `id` VARCHAR(36) PRIMARY KEY NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `price` DECIMAL(10, 2) NOT NULL,
    `image_url` VARCHAR(255),
    `category_id` VARCHAR(36),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`)
);