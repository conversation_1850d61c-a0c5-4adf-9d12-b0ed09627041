# 后端架构设计

## 1. 代码框架结构

基于当前项目结构，特别是 `services` 目录下的微服务（如 `user-service`），后端服务的代码组织结构将遵循以下模式：

### 根目录 (`pay-mall`)

*   `go.mod`, `go.work`, `go.work.sum`: Go 模块文件，用于管理整个项目的依赖和多模块工作区。
*   `services/`: 包含所有独立的微服务。每个微服务都是一个独立的 Go 模块。
*   `pkg/`: 存放公共包，例如 Protobuf 定义 (`pkg/proto`)、通用的工具函数、错误处理等。
*   `docs/`: 存放项目文档，如 `后端架构设计.md`、`项目需求清单.md` 等。

### 微服务目录 (`services/<service-name>`)

每个微服务（例如 `services/user-service`）内部将采用以下标准结构：

*   `cmd/`: 包含服务的入口点，通常是 `main.go` 文件，负责服务的初始化、路由注册和启动。
*   `internal/`: 存放服务内部的私有代码，不应被其他服务直接导入。
    *   `handler/`: 处理外部请求（HTTP/gRPC）的逻辑层。负责请求解析、参数校验、调用业务逻辑层，并返回响应。例如 `user_handler.go`。
    *   `service/` (或 `core/`): 业务逻辑层。包含核心业务规则和协调多个 `repository` 操作。
    *   `repository/`: 数据访问层。负责与数据库或其他持久化存储进行交互，提供数据存取接口。例如 `user_repository.go`。
    *   `model/` (可选): 定义服务内部的数据模型或实体。
*   `api/` (可选): 如果服务提供 gRPC 接口，这里可能存放 gRPC 服务的实现。
*   `go.mod`, `go.sum`: 微服务自身的 Go 模块文件，管理该服务的特定依赖。

## 2. 数据库表结构设计

为支付商城项目设计核心的数据库表结构。考虑到微服务架构，每个服务可能拥有自己的数据库，但这里提供一个逻辑上的整体视图。

### 用户服务 (User Service) 数据库

*   **`users` 表** (用户表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 用户唯一ID，UUID。
    *   `username` (VARCHAR(50), UNIQUE, NOT NULL): 用户名。
    *   `email` (VARCHAR(100), UNIQUE, NOT NULL): 邮箱地址。
    *   `password_hash` (VARCHAR(255), NOT NULL): 密码哈希值。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

### 商品服务 (Product Service) 数据库

*   **`products` 表** (商品表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 商品唯一ID，UUID。
    *   `name` (VARCHAR(255), NOT NULL): 商品名称。
    *   `description` (TEXT): 商品描述。
    *   `price` (DECIMAL(10, 2), NOT NULL): 商品价格。
    *   `image_url` (VARCHAR(255)): 商品图片URL。
    *   `category_id` (VARCHAR(36)): 商品分类ID (外键，关联 `categories` 表)。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

*   **`categories` 表** (商品分类表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 分类唯一ID，UUID。
    *   `name` (VARCHAR(100), UNIQUE, NOT NULL): 分类名称。
    *   `description` (TEXT): 分类描述。

### 订单服务 (Order Service) 数据库

*   **`orders` 表** (订单表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 订单唯一ID，UUID。
    *   `user_id` (VARCHAR(36), NOT NULL): 用户ID (外键，关联 `users` 表)。
    *   `total_amount` (DECIMAL(10, 2), NOT NULL): 订单总金额。
    *   `status` (VARCHAR(50), NOT NULL): 订单状态 (e.g., PENDING, PAID, SHIPPED, CANCELLED)。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

*   **`order_items` 表** (订单项表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 订单项唯一ID，UUID。
    *   `order_id` (VARCHAR(36), NOT NULL): 订单ID (外键，关联 `orders` 表)。
    *   `product_id` (VARCHAR(36), NOT NULL): 商品ID (外键，关联 `products` 表)。
    *   `quantity` (INT, NOT NULL): 商品数量。
    *   `price` (DECIMAL(10, 2), NOT NULL): 单价 (下单时的价格)。

### 支付服务 (Payment Service) 数据库

*   **`payments` 表** (支付记录表)
    *   `id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 支付记录唯一ID，UUID。
    *   `order_id` (VARCHAR(36), UNIQUE, NOT NULL): 订单ID (外键，关联 `orders` 表)。
    *   `user_id` (VARCHAR(36), NOT NULL): 用户ID (外键，关联 `users` 表)。
    *   `amount` (DECIMAL(10, 2), NOT NULL): 支付金额。
    *   `payment_method` (VARCHAR(50), NOT NULL): 支付方式 (e.g., ALIPAY, WECHAT_PAY, CREDIT_CARD)。
    *   `transaction_id` (VARCHAR(255), UNIQUE): 支付平台交易ID。
    *   `status` (VARCHAR(50), NOT NULL): 支付状态 (e.g., PENDING, SUCCESS, FAILED, REFUNDED)。
    *   `created_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP): 创建时间。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

### 库存服务 (Inventory Service) 数据库

*   **`inventory` 表** (库存表)
    *   `product_id` (VARCHAR(36), PRIMARY KEY, NOT NULL): 商品ID (外键，关联 `products` 表)。
    *   `stock` (INT, NOT NULL): 当前库存量。
    *   `reserved_stock` (INT, DEFAULT 0): 预留库存量 (例如，订单已创建但未支付)。
    *   `updated_at` (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP): 更新时间。

## 3. 事件领域

在支付商城系统中，关键业务事件将通过事件驱动架构进行发布和订阅，以实现服务间的解耦和异步通信。

### 事件流图

```mermaid
graph TD
    A[用户服务] -- 用户注册 --> B(UserRegistered Event)
    B -- 订阅 --> C[订单服务]
    B -- 订阅 --> D[其他服务]

    E[订单服务] -- 订单创建 --> F(OrderCreated Event)
    F -- 订阅 --> G[库存服务]
    F -- 订阅 --> H[支付服务]

    I[支付服务] -- 支付成功 --> J(PaymentSucceeded Event)
    J -- 订阅 --> E[订单服务]
    J -- 订阅 --> G[库存服务]
    J -- 订阅 --> K[通知服务]

    L[订单服务] -- 订单取消 --> M(OrderCancelled Event)
    M -- 订阅 --> G[库存服务]
    M -- 订阅 --> K[通知服务]

    G[库存服务] -- 库存更新 --> N(InventoryUpdated Event)
    N -- 订阅 --> E[订单服务]
    N -- 订阅 --> K[通知服务]
```

### 关键业务事件列表

1.  **事件名称**: `UserRegistered` (用户注册)
    *   **发布者**: 用户服务 (User Service)
    *   **订阅者**: 订单服务 (Order Service) (例如，为新用户创建默认地址或优惠券)、其他需要用户信息的服务。
    *   **事件内容**:
        ```json
        {
          "user_id": "string",
          "username": "string",
          "email": "string",
          "timestamp": "datetime"
        }
        ```

2.  **事件名称**: `OrderCreated` (订单创建)
    *   **发布者**: 订单服务 (Order Service)
    *   **订阅者**: 库存服务 (Inventory Service) (预留库存)、支付服务 (Payment Service) (准备支付)。
    *   **事件内容**:
        ```json
        {
          "order_id": "string",
          "user_id": "string",
          "total_amount": "decimal",
          "items": [
            {
              "product_id": "string",
              "quantity": "integer",
              "price": "decimal"
            }
          ],
          "timestamp": "datetime"
        }
        ```

3.  **事件名称**: `PaymentSucceeded` (支付成功)
    *   **发布者**: 支付服务 (Payment Service)
    *   **订阅者**: 订单服务 (Order Service) (更新订单状态为“已支付”)、库存服务 (Inventory Service) (扣减实际库存)、通知服务 (Notification Service) (发送支付成功通知)。
    *   **事件内容**:
        ```json
        {
          "payment_id": "string",
          "order_id": "string",
          "user_id": "string",
          "amount": "decimal",
          "transaction_id": "string",
          "timestamp": "datetime"
        }
        ```

4.  **事件名称**: `InventoryUpdated` (库存更新)
    *   **发布者**: 库存服务 (Inventory Service)
    *   **订阅者**: 订单服务 (Order Service) (例如，处理库存不足导致的订单失败)、商品服务 (Product Service) (更新商品库存显示)、通知服务 (Notification Service) (例如，库存预警)。
    *   **事件内容**:
        ```json
        {
          "product_id": "string",
          "new_stock": "integer",
          "change_amount": "integer",
          "reason": "string", // e.g., "order_paid", "order_cancelled", "manual_adjustment"
          "timestamp": "datetime"
        }
        ```

5.  **事件名称**: `OrderCancelled` (订单取消)
    *   **发布者**: 订单服务 (Order Service)
    *   **订阅者**: 库存服务 (Inventory Service) (释放预留库存)、通知服务 (Notification Service) (发送订单取消通知)。
    *   **事件内容**:
        ```json
        {
          "order_id": "string",
          "user_id": "string",
          "reason": "string",
          "timestamp": "datetime"
        }
        ```

## 4. 核心业务状态流转

为了更清晰地管理核心业务流程中的实体状态，我们将为订单和支付定义详细的状态流转。这些状态将作为数据库中 `status` 字段的值，并通过事件驱动机制进行状态转换。

### 4.1 订单状态流转

订单状态反映了订单从创建到完成或取消的整个生命周期。

#### 4.1.1 订单状态定义

*   **`PENDING` (待处理)**: 订单已创建，但尚未进行库存预留或支付。
*   **`RESERVED` (已预留库存)**: 订单商品库存已成功预留，等待支付。
*   **`PAID` (已支付)**: 订单已成功支付。
*   **`SHIPPED` (已发货)**: 订单商品已发货。
*   **`DELIVERED` (已送达)**: 订单商品已送达用户。
*   **`CANCELLED` (已取消)**: 订单在支付前或支付后被取消。
*   **`REFUNDED` (已退款)**: 订单已完成退款。

#### 4.1.2 订单状态流转图

```mermaid
stateDiagram-v2
    [*] --> PENDING: 订单创建
    PENDING --> RESERVED: 库存预留成功 (InventoryReserved Event)
    RESERVED --> PAID: 支付成功 (PaymentSucceeded Event)
    PAID --> SHIPPED: 商品发货
    SHIPPED --> DELIVERED: 商品送达
    PENDING --> CANCELLED: 用户取消 / 超时未支付
    RESERVED --> CANCELLED: 用户取消 / 支付失败 (PaymentFailed Event)
    PAID --> REFUNDED: 用户退款 / 异常退款
    CANCELLED --> [*]
    DELIVERED --> [*]
    REFUNDED --> [*]
```

### 4.2 支付状态流转

支付状态反映了支付请求从发起、处理到完成或失败的生命周期。

#### 4.2.1 支付状态定义

*   **`PENDING` (待支付)**: 支付请求已创建，等待用户完成支付。
*   **`SUCCESS` (支付成功)**: 支付已成功完成。
*   **`FAILED` (支付失败)**: 支付尝试失败。
*   **`REFUNDING` (退款中)**: 正在处理退款请求。
*   **`REFUNDED` (已退款)**: 退款已成功完成。

#### 4.2.2 支付状态流转图

```mermaid
stateDiagram-v2
    [*] --> PENDING: 发起支付请求
    PENDING --> SUCCESS: 支付成功回调
    PENDING --> FAILED: 支付失败回调 / 超时
    SUCCESS --> REFUNDING: 发起退款请求
    REFUNDING --> REFUNDED: 退款成功回调
    REFUNDING --> SUCCESS: 退款失败 (回滚到成功状态)
    FAILED --> [*]
    REFUNDED --> [*]
```